import { supabase } from '@/integrations/supabase/client'
import type {
  Event,
  NewEvent,
  EventWithInterests,
  EventFilters,
  EventSortOptions,
  EventListResponse,
  EventInterest,
  NewEventInterest,
  AttendeeProfile,
  AttendeeContactInfo,
  AttendeeCSVData
} from '../types'

export class EventService {
  /**
   * Get all events with optional filtering and pagination
   */
  static async getEvents(
    filters: EventFilters = {},
    sort: EventSortOptions = { field: 'start_date', direction: 'asc' },
    page: number = 1,
    limit: number = 20
  ): Promise<EventListResponse> {
    let query = supabase
      .from('events')
      .select(`
        *,
        creator:created_by_user_id (
          id,
          first_name,
          last_name,
          avatar_url
        ),
        interests:event_interests (
          id,
          user_id,
          is_interested,
          is_attending,
          gdpr_consent_given,
          visibility_consent_given,
          note,
          created_at
        ),
        event_netzero_categories (
          subcategory_id,
          subcategory:subcategory_id (
            id,
            name,
            description,
            category:category_id (
              id,
              name,
              description
            )
          )
        )
      `)

    // Apply filters
    if (filters.location_type && filters.location_type.length > 0) {
      query = query.in('location_type', filters.location_type)
    }

    if (filters.city && filters.city.length > 0) {
      query = query.in('city', filters.city)
    }

    if (filters.start_date_from) {
      query = query.gte('start_date', filters.start_date_from)
    }

    if (filters.start_date_to) {
      query = query.lte('start_date', filters.start_date_to)
    }

    if (filters.tags && filters.tags.length > 0) {
      query = query.overlaps('tags', filters.tags)
    }

    if (filters.search) {
      query = query.or(`name.ilike.%${filters.search}%,description.ilike.%${filters.search}%,organizer_name.ilike.%${filters.search}%`)
    }

    // Apply sorting
    query = query.order(sort.field, { ascending: sort.direction === 'asc' })

    // Apply pagination
    const from = (page - 1) * limit
    const to = from + limit - 1
    query = query.range(from, to)

    const { data, error, count } = await query

    if (error) {
      throw new Error(`Failed to fetch events: ${error.message}`)
    }

    // Transform data to include computed fields
    const transformedData: EventWithInterests[] = (data || []).map(item => ({
      ...item,
      creator: item.creator,
      interests: item.interests || [],
      interest_count: item.interests?.filter(i => i.is_interested).length || 0,
      attending_count: item.interests?.filter(i => i.is_attending).length || 0,
      user_interest: null // Will be set by getUserInterest if needed
    }))

    return {
      data: transformedData,
      count: count || 0,
      page,
      limit,
      total_pages: Math.ceil((count || 0) / limit)
    }
  }

  /**
   * Get a single event by ID
   */
  static async getEventById(id: string): Promise<EventWithInterests | null> {
    const { data, error } = await supabase
      .from('events')
      .select(`
        *,
        creator:created_by_user_id (
          id,
          first_name,
          last_name,
          avatar_url
        ),
        interests:event_interests (
          id,
          user_id,
          is_interested,
          is_attending,
          gdpr_consent_given,
          gdpr_consent_date,
          visibility_consent_given,
          visibility_consent_date,
          note,
          created_at,
          user:user_id (
            id,
            first_name,
            last_name,
            avatar_url
          )
        )
      `)
      .eq('id', id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null // Not found
      }
      throw new Error(`Failed to fetch event: ${error.message}`)
    }

    return {
      ...data,
      creator: data.creator,
      interests: data.interests || [],
      interest_count: data.interests?.filter(i => i.is_interested).length || 0,
      attending_count: data.interests?.filter(i => i.is_attending).length || 0,
      user_interest: null
    }
  }

  /**
   * Create a new event
   */
  static async createEvent(
    eventData: Omit<NewEvent, 'created_by_user_id'> & {
      net_zero_categories?: string[];
      primary_industry_id?: string;
      relevant_industry_ids?: string[]
    }
  ): Promise<Event> {
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      throw new Error('User must be authenticated to create events')
    }

    // Extract net zero categories and industries from eventData
    const { net_zero_categories, relevant_industry_ids, ...eventDataWithoutCategories } = eventData

    const { data, error } = await supabase
      .from('events')
      .insert({
        ...eventDataWithoutCategories,
        created_by_user_id: user.id
      })
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to create event: ${error.message}`)
    }

    // Add net zero categories if provided
    if (net_zero_categories && net_zero_categories.length > 0) {
      await this.updateEventCategories(data.id, net_zero_categories)
    }

    // Add relevant industries if provided
    if (relevant_industry_ids && relevant_industry_ids.length > 0) {
      await this.updateEventRelevantIndustries(data.id, relevant_industry_ids)
    }

    return data
  }

  /**
   * Update event net zero categories
   */
  static async updateEventCategories(eventId: string, categoryIds: string[]): Promise<void> {
    // First, remove existing categories
    const { error: deleteError } = await supabase
      .from('event_netzero_categories')
      .delete()
      .eq('event_id', eventId)

    if (deleteError) {
      throw new Error(`Failed to remove existing categories: ${deleteError.message}`)
    }

    // Then, add new categories
    if (categoryIds.length > 0) {
      const categoryData = categoryIds.map(subcategoryId => ({
        event_id: eventId,
        subcategory_id: subcategoryId
      }))

      const { error: insertError } = await supabase
        .from('event_netzero_categories')
        .insert(categoryData)

      if (insertError) {
        throw new Error(`Failed to add new categories: ${insertError.message}`)
      }
    }
  }

  /**
   * Update event relevant industries
   */
  static async updateEventRelevantIndustries(eventId: string, industryIds: string[]): Promise<void> {
    // First, remove existing relevant industries
    const { error: deleteError } = await supabase
      .from('event_relevant_industries' as any)
      .delete()
      .eq('event_id', eventId)

    if (deleteError) {
      throw new Error(`Failed to remove existing relevant industries: ${deleteError.message}`)
    }

    // Then, add new relevant industries
    if (industryIds.length > 0) {
      const industryData = industryIds.map(industryId => ({
        event_id: eventId,
        industry_id: industryId
      }))

      const { error: insertError } = await supabase
        .from('event_relevant_industries' as any)
        .insert(industryData)

      if (insertError) {
        throw new Error(`Failed to add new relevant industries: ${insertError.message}`)
      }
    }
  }

  /**
   * Update an existing event
   */
  static async updateEvent(
    id: string,
    eventData: Partial<Omit<NewEvent, 'created_by_user_id'>> & {
      net_zero_categories?: string[];
      primary_industry_id?: string;
      relevant_industry_ids?: string[]
    }
  ): Promise<Event> {
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      throw new Error('User must be authenticated to update events')
    }

    // Extract net zero categories and industries from eventData
    const { net_zero_categories, relevant_industry_ids, ...eventDataWithoutCategories } = eventData

    const { data, error } = await supabase
      .from('events')
      .update(eventDataWithoutCategories)
      .eq('id', id)
      .eq('created_by_user_id', user.id)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to update event: ${error.message}`)
    }

    // Update net zero categories if provided
    if (net_zero_categories !== undefined) {
      await this.updateEventCategories(id, net_zero_categories)
    }

    // Update relevant industries if provided
    if (relevant_industry_ids !== undefined) {
      await this.updateEventRelevantIndustries(id, relevant_industry_ids)
    }

    return data
  }

  /**
   * Delete an event
   */
  static async deleteEvent(id: string): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User must be authenticated to delete events')
    }

    const { error } = await supabase
      .from('events')
      .delete()
      .eq('id', id)
      .eq('created_by_user_id', user.id)

    if (error) {
      throw new Error(`Failed to delete event: ${error.message}`)
    }
  }

  /**
   * Get events created by a specific user
   */
  static async getEventsByUser(userId: string): Promise<EventWithInterests[]> {
    const { data, error } = await supabase
      .from('events')
      .select(`
        *,
        creator:created_by_user_id (
          id,
          first_name,
          last_name,
          avatar_url
        ),
        interests:event_interests (
          id,
          user_id,
          is_interested,
          is_attending,
          gdpr_consent_given,
          visibility_consent_given,
          note,
          created_at
        )
      `)
      .eq('created_by_user_id', userId)
      .order('start_date', { ascending: true })

    if (error) {
      throw new Error(`Failed to fetch user events: ${error.message}`)
    }

    return (data || []).map(item => ({
      ...item,
      creator: item.creator,
      interests: item.interests || [],
      interest_count: item.interests?.filter(i => i.is_interested).length || 0,
      attending_count: item.interests?.filter(i => i.is_attending).length || 0,
      user_interest: null
    }))
  }

  /**
   * Express interest in an event
   */
  static async expressInterest(
    eventId: string,
    interestData: Omit<NewEventInterest, 'event_id' | 'user_id'>
  ): Promise<EventInterest> {
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      throw new Error('User must be authenticated to express interest')
    }

    // Add consent timestamps if consent is given
    const finalInterestData = {
      ...interestData,
      event_id: eventId,
      user_id: user.id,
      gdpr_consent_date: interestData.gdpr_consent_given ? new Date().toISOString() : null,
      visibility_consent_date: interestData.visibility_consent_given ? new Date().toISOString() : null
    }

    const { data, error } = await supabase
      .from('event_interests')
      .upsert(finalInterestData, {
        onConflict: 'event_id,user_id'
      })
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to express interest: ${error.message}`)
    }

    return data
  }

  /**
   * Get user's interest for a specific event
   */
  static async getUserInterest(eventId: string, userId: string): Promise<EventInterest | null> {
    const { data, error } = await supabase
      .from('event_interests')
      .select('*')
      .eq('event_id', eventId)
      .eq('user_id', userId)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null // Not found
      }
      throw new Error(`Failed to fetch user interest: ${error.message}`)
    }

    return data
  }

  /**
   * Get attendees for an event (for organizers with GDPR consent)
   */
  static async getEventAttendees(eventId: string): Promise<AttendeeProfile[]> {
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      throw new Error('User must be authenticated to view attendees')
    }

    // First check if the user is the event organizer
    const { data: event, error: eventError } = await supabase
      .from('events')
      .select('created_by_user_id')
      .eq('id', eventId)
      .single()

    if (eventError || !event) {
      throw new Error('Event not found or access denied')
    }

    if (event.created_by_user_id !== user.id) {
      throw new Error('Only event organizers can view attendee details')
    }

    // Get attendees with GDPR consent
    const { data, error } = await supabase
      .from('event_interests')
      .select(`
        id,
        is_interested,
        is_attending,
        gdpr_consent_given,
        gdpr_consent_date,
        note,
        created_at,
        user:user_id (
          id,
          first_name,
          last_name,
          job_title,
          organisation_name,
          avatar_url
        )
      `)
      .eq('event_id', eventId)
      .eq('gdpr_consent_given', true)
      .order('created_at', { ascending: false })

    if (error) {
      throw new Error(`Failed to fetch attendees: ${error.message}`)
    }

    return (data || []).map(item => ({
      id: item.user?.id || '',
      first_name: item.user?.first_name,
      last_name: item.user?.last_name,
      job_title: item.user?.job_title,
      organisation_name: item.user?.organisation_name,
      avatar_url: item.user?.avatar_url,
      gdpr_consent_given: item.gdpr_consent_given || false,
      gdpr_consent_date: item.gdpr_consent_date,
      is_interested: item.is_interested || false,
      is_attending: item.is_attending || false,
      note: item.note,
      created_at: item.created_at
    }))
  }

  /**
   * Get events that a user has expressed interest in
   */
  static async getUserInterestedEvents(userId?: string): Promise<EventWithInterests[]> {
    const { data: { user } } = await supabase.auth.getUser()
    const targetUserId = userId || user?.id

    if (!targetUserId) {
      throw new Error('User must be authenticated or userId must be provided')
    }

    const { data, error } = await supabase
      .from('event_interests')
      .select(`
        id,
        is_interested,
        is_attending,
        gdpr_consent_given,
        gdpr_consent_date,
        visibility_consent_given,
        visibility_consent_date,
        note,
        created_at,
        event:event_id (
          *,
          creator:created_by_user_id (
            id,
            first_name,
            last_name,
            avatar_url
          )
        )
      `)
      .eq('user_id', targetUserId)
      .eq('is_interested', true)
      .order('created_at', { ascending: false })

    if (error) {
      throw new Error(`Failed to fetch interested events: ${error.message}`)
    }

    return (data || []).map(item => ({
      ...item.event,
      creator: item.event?.creator,
      interests: [],
      interest_count: 0,
      attending_count: 0,
      user_interest: {
        id: item.id,
        event_id: item.event?.id || '',
        user_id: targetUserId,
        is_interested: item.is_interested,
        is_attending: item.is_attending,
        gdpr_consent_given: item.gdpr_consent_given,
        gdpr_consent_date: item.gdpr_consent_date,
        visibility_consent_given: item.visibility_consent_given,
        visibility_consent_date: item.visibility_consent_date,
        note: item.note,
        created_at: item.created_at,
        updated_at: item.created_at
      }
    })).filter(event => event.id) // Filter out any null events
  }

  /**
   * Get events that a user is registered for (attending)
   */
  static async getUserRegisteredEvents(userId?: string): Promise<EventWithInterests[]> {
    const { data: { user } } = await supabase.auth.getUser()
    const targetUserId = userId || user?.id

    if (!targetUserId) {
      throw new Error('User must be authenticated or userId must be provided')
    }

    const { data, error } = await supabase
      .from('event_interests')
      .select(`
        id,
        is_interested,
        is_attending,
        gdpr_consent_given,
        gdpr_consent_date,
        visibility_consent_given,
        visibility_consent_date,
        note,
        created_at,
        event:event_id (
          *,
          creator:created_by_user_id (
            id,
            first_name,
            last_name,
            avatar_url
          )
        )
      `)
      .eq('user_id', targetUserId)
      .eq('is_attending', true)
      .order('created_at', { ascending: false })

    if (error) {
      throw new Error(`Failed to fetch registered events: ${error.message}`)
    }

    return (data || []).map(item => ({
      ...item.event,
      creator: item.event?.creator,
      interests: [],
      interest_count: 0,
      attending_count: 0,
      user_interest: {
        id: item.id,
        event_id: item.event?.id || '',
        user_id: targetUserId,
        is_interested: item.is_interested,
        is_attending: item.is_attending,
        gdpr_consent_given: item.gdpr_consent_given,
        gdpr_consent_date: item.gdpr_consent_date,
        visibility_consent_given: item.visibility_consent_given,
        visibility_consent_date: item.visibility_consent_date,
        note: item.note,
        created_at: item.created_at,
        updated_at: item.created_at
      }
    })).filter(event => event.id) // Filter out any null events
  }

  /**
   * Get publicly visible attendees for an event (users who consented to visibility)
   */
  static async getPublicAttendees(eventId: string): Promise<AttendeeProfile[]> {
    const { data, error } = await supabase
      .from('event_interests')
      .select(`
        id,
        is_interested,
        is_attending,
        visibility_consent_given,
        visibility_consent_date,
        note,
        created_at,
        user:user_id (
          id,
          first_name,
          last_name,
          job_title,
          organisation_name,
          avatar_url,
          profile_visible
        )
      `)
      .eq('event_id', eventId)
      .eq('visibility_consent_given', true)
      .order('created_at', { ascending: false })

    if (error) {
      throw new Error(`Failed to fetch public attendees: ${error.message}`)
    }

    // Filter for users who have both visibility consent and profile_visible set to true
    return (data || [])
      .filter(item => item.user?.profile_visible === true)
      .map(item => ({
        id: item.user?.id || '',
        first_name: item.user?.first_name,
        last_name: item.user?.last_name,
        job_title: item.user?.job_title,
        organisation_name: item.user?.organisation_name,
        avatar_url: item.user?.avatar_url,
        gdpr_consent_given: false, // Not relevant for public view
        gdpr_consent_date: undefined,
        visibility_consent_given: item.visibility_consent_given || false,
        visibility_consent_date: item.visibility_consent_date,
        is_interested: item.is_interested || false,
        is_attending: item.is_attending || false,
        note: item.note,
        created_at: item.created_at
      }))
  }

  /**
   * Get attendees with contact information for an event (secure view with email access)
   * Only available to event organizers, only includes users who gave GDPR consent
   */
  static async getEventAttendeesWithContact(eventId: string): Promise<AttendeeContactInfo[]> {
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      throw new Error('User must be authenticated to view attendee contact information')
    }

    // Query the secure view which handles all security checks via RLS
    const { data, error } = await supabase
      .from('event_attendees_with_contact')
      .select('*')
      .eq('event_id', eventId)
      .order('registered_at', { ascending: false })

    if (error) {
      throw new Error(`Failed to fetch attendee contact information: ${error.message}`)
    }

    return (data || []).map(item => ({
      interest_id: item.interest_id,
      event_id: item.event_id,
      user_id: item.user_id,
      first_name: item.first_name,
      last_name: item.last_name,
      job_title: item.job_title,
      organisation_name: item.organisation_name,
      avatar_url: item.avatar_url,
      email: item.email,
      is_interested: item.is_interested || false,
      is_attending: item.is_attending || false,
      gdpr_consent_given: item.gdpr_consent_given || false,
      gdpr_consent_date: item.gdpr_consent_date,
      visibility_consent_given: item.visibility_consent_given || false,
      visibility_consent_date: item.visibility_consent_date,
      note: item.note,
      registered_at: item.registered_at,
      event_name: item.event_name,
      event_start_date: item.event_start_date
    }))
  }

  /**
   * Export attendee contact information to CSV format
   */
  static async exportAttendeesCSV(eventId: string): Promise<string> {
    const attendees = await this.getEventAttendeesWithContact(eventId)
    
    if (attendees.length === 0) {
      throw new Error('No attendees with contact consent found for this event')
    }

    // Convert to CSV format
    const csvData: AttendeeCSVData[] = attendees.map(attendee => ({
      'First Name': attendee.first_name || '',
      'Last Name': attendee.last_name || '',
      'Email': attendee.email || '',
      'Job Title': attendee.job_title || '',
      'Organisation': attendee.organisation_name || '',
      'Interest Status': attendee.is_interested ? 'Interested' : 'Not Interested',
      'Attendance Status': attendee.is_attending ? 'Attending' : 'Not Attending',
      'Registration Date': new Date(attendee.registered_at).toLocaleDateString(),
      'GDPR Consent Date': attendee.gdpr_consent_date ? new Date(attendee.gdpr_consent_date).toLocaleDateString() : '',
      'Note': attendee.note || ''
    }))

    // Generate CSV string
    const headers = Object.keys(csvData[0])
    const csvRows = [
      headers.join(','),
      ...csvData.map(row => 
        headers.map(header => {
          const value = row[header as keyof AttendeeCSVData] || ''
          // Escape commas and quotes in CSV values
          return `"${value.toString().replace(/"/g, '""')}"`
        }).join(',')
      )
    ]

    return csvRows.join('\n')
  }

  /**
   * Get count of attendees with contact consent for an event
   */
  static async getAttendeesWithContactCount(eventId: string): Promise<number> {
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      throw new Error('User must be authenticated to view attendee count')
    }

    const { count, error } = await supabase
      .from('event_attendees_with_contact')
      .select('*', { count: 'exact', head: true })
      .eq('event_id', eventId)

    if (error) {
      throw new Error(`Failed to fetch attendee count: ${error.message}`)
    }

    return count || 0
  }
}
