// Event types for the Net Zero Nexus Hub Events Directory

export interface Event {
  id: string
  created_by_user_id: string
  name: string
  description: string | null
  organizer_name: string
  organizer_email: string | null
  organizer_phone: string | null
  start_date: string
  start_time: string | null
  end_date: string | null
  end_time: string | null
  timezone: string | null
  location_type: EventLocationType
  venue_name: string | null
  address_line_1: string | null
  address_line_2: string | null
  city: string | null
  postcode: string | null
  online_meeting_url: string | null
  online_meeting_details: string | null
  max_attendees: number | null
  registration_required: boolean | null
  registration_url: string | null
  event_url: string | null
  tags: string[] | null
  target_industry_id: string | null
  created_at: string
  updated_at: string
}

export interface NewEvent {
  created_by_user_id?: string
  name: string
  description?: string | null
  organizer_name: string
  organizer_email?: string | null
  organizer_phone?: string | null
  start_date: string
  start_time?: string | null
  end_date?: string | null
  end_time?: string | null
  timezone?: string | null
  location_type: EventLocationType
  venue_name?: string | null
  address_line_1?: string | null
  address_line_2?: string | null
  city?: string | null
  postcode?: string | null
  online_meeting_url?: string | null
  online_meeting_details?: string | null
  max_attendees?: number | null
  registration_required?: boolean | null
  registration_url?: string | null
  event_url?: string | null
  tags?: string[] | null
  target_industry_id?: string | null
}

export interface EventInterest {
  id: string
  event_id: string
  user_id: string
  is_interested: boolean | null
  is_attending: boolean | null
  gdpr_consent_given: boolean | null
  gdpr_consent_date: string | null
  visibility_consent_given: boolean | null
  visibility_consent_date: string | null
  note: string | null
  created_at: string
  updated_at: string
}

export interface NewEventInterest {
  event_id: string
  user_id?: string
  is_interested?: boolean | null
  is_attending?: boolean | null
  gdpr_consent_given?: boolean | null
  gdpr_consent_date?: string | null
  visibility_consent_given?: boolean | null
  visibility_consent_date?: string | null
  note?: string | null
}

// Enums
export type EventLocationType = 'online' | 'face_to_face' | 'hybrid'

// Form data interfaces
export interface EventFormData {
  name: string
  description?: string
  organizer_name: string
  organizer_email?: string
  organizer_phone?: string
  start_date: string
  start_time?: string
  end_date?: string
  end_time?: string
  timezone?: string
  location_type: EventLocationType
  venue_name?: string
  address_line_1?: string
  address_line_2?: string
  city?: string
  postcode?: string
  online_meeting_url?: string
  online_meeting_details?: string
  max_attendees?: number
  registration_required?: boolean
  registration_url?: string
  event_url?: string
  tags?: string[]
  net_zero_categories?: string[] // Array of subcategory IDs
  target_industry_id?: string // DEPRECATED: Use primary_industry_id and relevant_industry_ids instead
  primary_industry_id?: string // Primary industry (single select, like businesses.main_industry_id)
  relevant_industry_ids?: string[] // Array of industry IDs (multi-select, like business_target_industries)
}

// Calendar integration types
export interface CalendarEvent {
  title: string
  description?: string
  startDate: string
  startTime?: string
  endDate?: string
  endTime?: string
  timezone?: string
  location?: string
  url?: string
}

export type CalendarService = 'google' | 'outlook' | 'office365' | 'yahoo' | 'ical'

// Extended types with related data
export interface EventWithCreator extends Event {
  creator?: {
    id: string
    first_name?: string
    last_name?: string
    avatar_url?: string
  }
}

export interface EventWithInterests extends EventWithCreator {
  interests?: EventInterest[]
  interest_count?: number
  attending_count?: number
  user_interest?: EventInterest | null
}

// Interest form data
export interface EventInterestFormData {
  is_interested: boolean
  is_attending: boolean
  gdpr_consent_given: boolean
  visibility_consent_given: boolean
  note?: string
}

// Filter and sort interfaces
export interface EventFilters {
  location_type?: EventLocationType[]
  city?: string[]
  start_date_from?: string
  start_date_to?: string
  tags?: string[]
  search?: string
}

export interface EventSortOptions {
  field: 'start_date' | 'name' | 'created_at' | 'interest_count'
  direction: 'asc' | 'desc'
}

export interface EventListResponse {
  data: EventWithInterests[]
  count: number
  page: number
  limit: number
  total_pages: number
}

// Constants
export const EVENT_LOCATION_TYPES = [
  { value: 'online', label: 'Online', icon: '💻' },
  { value: 'face_to_face', label: 'Face to Face', icon: '🏢' },
  { value: 'hybrid', label: 'Hybrid', icon: '🔄' }
] as const

export const DEFAULT_TIMEZONE = 'Europe/London'

// Utility types for attendee management
export interface AttendeeProfile {
  id: string
  first_name?: string
  last_name?: string
  job_title?: string
  organisation_name?: string
  avatar_url?: string
  email?: string
  gdpr_consent_given: boolean
  gdpr_consent_date?: string
  visibility_consent_given?: boolean
  visibility_consent_date?: string
  is_interested: boolean
  is_attending: boolean
  note?: string
  created_at: string
}

// GDPR consent tracking
export interface GDPRConsentData {
  consent_given: boolean
  consent_date: string
  purpose: string
  user_id: string
  event_id: string
}

// Attendee contact information (for organizers with GDPR consent)
export interface AttendeeContactInfo {
  interest_id: string
  event_id: string
  user_id: string
  first_name?: string
  last_name?: string
  job_title?: string
  organisation_name?: string
  avatar_url?: string
  email?: string  // Only visible if GDPR consent given
  is_interested: boolean
  is_attending: boolean
  gdpr_consent_given: boolean
  gdpr_consent_date?: string
  visibility_consent_given?: boolean
  visibility_consent_date?: string
  note?: string
  registered_at: string
  event_name: string
  event_start_date: string
}

// CSV export data structure
export interface AttendeeCSVData {
  'First Name': string
  'Last Name': string
  'Email': string
  'Job Title': string
  'Organisation': string
  'Interest Status': string
  'Attendance Status': string
  'Registration Date': string
  'GDPR Consent Date': string
  'Note': string
}
