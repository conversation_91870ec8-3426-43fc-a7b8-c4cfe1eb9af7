
import { useNavigate } from 'react-router-dom'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { ExternalLink, Calendar, Users, Heart, HandHeart, Eye, Leaf, Building2 } from 'lucide-react'
import { NetZeroCategoryDisplay } from '@/components/netzero/NetZeroCategoryDisplay'
import { IndustryDisplay } from '@/components/industries/IndustryDisplay'
import type { FundingOpportunityWithInterests } from '../types'
import { FUNDING_TYPES, DEFAULT_CURRENCY } from '../types'

interface FundingOpportunityCardProps {
  opportunity: FundingOpportunityWithInterests
}

const FundingOpportunityCard = ({ opportunity }: FundingOpportunityCardProps) => {
  const navigate = useNavigate()

  const fundingTypeInfo = FUNDING_TYPES.find(type => type.value === opportunity.funding_type)

  const formatDate = (dateString: string | null) => {
    if (!dateString) return null
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    })
  }

  const formatAmount = (amount: number | null) => {
    if (!amount) return null
    return `£${amount.toLocaleString()}`
  }

  const getAmountDisplay = () => {
    if (opportunity.amount_min && opportunity.amount_max) {
      return `${formatAmount(opportunity.amount_min)} - ${formatAmount(opportunity.amount_max)}`
    } else if (opportunity.amount_min) {
      return `From ${formatAmount(opportunity.amount_min)}`
    } else if (opportunity.amount_max) {
      return `Up to ${formatAmount(opportunity.amount_max)}`
    }
    return null
  }

  const isDeadlineSoon = () => {
    if (!opportunity.deadline_date) return false
    const deadline = new Date(opportunity.deadline_date)
    const now = new Date()
    const daysUntilDeadline = Math.ceil((deadline.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
    return daysUntilDeadline <= 7 && daysUntilDeadline >= 0
  }

  const isDeadlinePassed = () => {
    if (!opportunity.deadline_date) return false
    const deadline = new Date(opportunity.deadline_date)
    const now = new Date()
    return deadline < now
  }



  return (
    <Card className={`h-full transition-all hover:shadow-md ${isDeadlinePassed() ? 'opacity-75' : ''}`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between gap-3">
          <div className="flex-1 min-w-0">
            <CardTitle className="text-lg leading-tight">{opportunity.name}</CardTitle>
            <CardDescription className="mt-1">
              {opportunity.organization_name}
            </CardDescription>
          </div>
          
          <div className="flex flex-col gap-2 items-end">
            <Badge variant={isDeadlinePassed() ? "destructive" : "secondary"}>
              {fundingTypeInfo?.label || opportunity.funding_type}
            </Badge>
            
            {isDeadlineSoon() && !isDeadlinePassed() && (
              <Badge variant="destructive" className="text-xs">
                Deadline Soon
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Description */}
        {opportunity.description && (
          <p className="text-sm text-muted-foreground line-clamp-3">
            {opportunity.description}
          </p>
        )}

        {/* Net Zero Categories */}
        {(opportunity as any).funding_netzero_categories && (opportunity as any).funding_netzero_categories.length > 0 && (
          <div className="space-y-2">
            <div className="flex items-center gap-1 text-xs font-medium text-muted-foreground">
              <Leaf className="w-3 h-3" />
              Net Zero Categories
            </div>
            <NetZeroCategoryDisplay
              categories={(opportunity as any).funding_netzero_categories.map((cat: any) => ({
                id: cat.subcategory.id,
                name: cat.subcategory.name,
                description: cat.subcategory.description,
                category: {
                  id: cat.subcategory.category.id,
                  name: cat.subcategory.category.name,
                  description: cat.subcategory.category.description
                }
              }))}
              variant="badges-only"
              className="text-xs"
            />
          </div>
        )}

        {/* Industries */}
        {((opportunity as any).primary_industry || (opportunity as any).target_industry) && (
          <div className="space-y-2">
            <div className="flex items-center gap-1 text-xs font-medium text-muted-foreground">
              <Building2 className="w-3 h-3" />
              Industries
            </div>
            <div className="flex flex-wrap gap-1">
              {(opportunity as any).primary_industry && (
                <Badge variant="default" className="text-xs bg-blue-600 hover:bg-blue-700">
                  {(opportunity as any).primary_industry.name} (Primary)
                </Badge>
              )}
              {(opportunity as any).target_industry && (opportunity as any).target_industry.id !== (opportunity as any).primary_industry?.id && (
                <Badge variant="secondary" className="text-xs">
                  {(opportunity as any).target_industry.name}
                </Badge>
              )}
            </div>
          </div>
        )}

        {/* Amount */}
        {getAmountDisplay() && (
          <div className="text-sm">
            <span className="font-medium">Amount: </span>
            {getAmountDisplay()}
          </div>
        )}

        {/* Dates */}
        <div className="flex flex-wrap gap-4 text-sm text-muted-foreground">
          {opportunity.date_listed && (
            <div className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              <span>Listed {formatDate(opportunity.date_listed)}</span>
            </div>
          )}
          
          {opportunity.deadline_date && (
            <div className={`flex items-center gap-1 ${isDeadlinePassed() ? 'text-destructive' : isDeadlineSoon() ? 'text-orange-600' : ''}`}>
              <Calendar className="h-3 w-3" />
              <span>
                Deadline {formatDate(opportunity.deadline_date)}
                {isDeadlinePassed() && ' (Passed)'}
              </span>
            </div>
          )}
        </div>

        {/* Creator */}
        {opportunity.creator && (
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Avatar className="h-5 w-5">
              <AvatarImage src={opportunity.creator.avatar_url || undefined} />
              <AvatarFallback className="text-xs">
                {opportunity.creator.first_name?.[0]}{opportunity.creator.last_name?.[0]}
              </AvatarFallback>
            </Avatar>
            <span>
              Added by {opportunity.creator.first_name} {opportunity.creator.last_name}
            </span>
          </div>
        )}

        {/* Interest Stats */}
        <div className="flex items-center gap-4 text-sm text-muted-foreground">
          <div className="flex items-center gap-1">
            <Heart className="h-3 w-3" />
            <span>{opportunity.interest_count || 0} interested</span>
          </div>
          
          <div className="flex items-center gap-1">
            <Users className="h-3 w-3" />
            <span>{opportunity.collaboration_count || 0} want to collaborate</span>
          </div>
        </div>

        {/* Actions */}
        <div className="space-y-2 pt-2">
          {/* Primary Action - View Details */}
          <Button
            variant="default"
            size="sm"
            onClick={() => navigate(`/funding/${opportunity.id}`)}
            className="w-full flex items-center gap-2"
          >
            <Eye className="h-3 w-3" />
            View Details & Connect
          </Button>
          

        </div>
      </CardContent>
    </Card>
  )
}

export default FundingOpportunityCard
