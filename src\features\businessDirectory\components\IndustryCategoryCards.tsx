import React from 'react';
import CategoryCards from '@/components/shared/CategoryCards';
import { UKIndustryService } from '@/services/ukIndustryService';
import type { CategoryCount, ContentType } from '@/components/shared/CategoryCards';

interface IndustryCategoryCardsProps {
  contentType: ContentType;
  onCategorySelect: (categoryId: string, categoryName: string) => void;
  selectedCategoryId?: string;
  title?: string;
  description?: string;
}

const IndustryCategoryCards: React.FC<IndustryCategoryCardsProps> = ({
  contentType,
  onCategorySelect,
  selectedCategoryId,
  title,
  description
}) => {
  const fetchCategoryCounts = async (): Promise<CategoryCount[]> => {
    switch (contentType) {
      case 'businesses':
        return await UKIndustryService.getIndustryBusinessCounts();
      case 'events':
        return await UKIndustryService.getIndustryEventCounts();
      case 'funding':
        return await UKIndustryService.getIndustryFundingCounts();
      default:
        return await UKIndustryService.getIndustryBusinessCounts();
    }
  };

  return (
    <CategoryCards
      categoryType="industry"
      contentType={contentType}
      onCategorySelect={onCategorySelect}
      selectedCategoryId={selectedCategoryId}
      fetchCategoryCounts={fetchCategoryCounts}
      title={title}
      description={description}
    />
  );
};

export default IndustryCategoryCards;
