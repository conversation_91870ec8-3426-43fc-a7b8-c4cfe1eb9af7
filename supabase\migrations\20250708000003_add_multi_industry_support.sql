-- Add multi-industry support to Events and Funding
-- This migration creates junction tables for events and funding opportunities to support multiple relevant industries
-- Following the same pattern as businesses: primary_industry_id + junction table for multiple relevant industries

-- Add primary industry columns to events and funding (similar to businesses.main_industry_id)
ALTER TABLE public.events 
ADD COLUMN IF NOT EXISTS primary_industry_id UUID REFERENCES public.uk_industries(id);

ALTER TABLE public.funding_opportunities 
ADD COLUMN IF NOT EXISTS primary_industry_id UUID REFERENCES public.uk_industries(id);

-- Create junction table for event relevant industries (multi-select, similar to business_target_industries)
CREATE TABLE IF NOT EXISTS public.event_relevant_industries (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    event_id UUID NOT NULL REFERENCES public.events(id) ON DELETE CASCADE,
    industry_id UUID NOT NULL REFERENCES public.uk_industries(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(event_id, industry_id) -- Prevent duplicate relevant industries
);

-- Create junction table for funding opportunity relevant industries (multi-select, similar to business_target_industries)
CREATE TABLE IF NOT EXISTS public.funding_relevant_industries (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    funding_opportunity_id UUID NOT NULL REFERENCES public.funding_opportunities(id) ON DELETE CASCADE,
    industry_id UUID NOT NULL REFERENCES public.uk_industries(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(funding_opportunity_id, industry_id) -- Prevent duplicate relevant industries
);

-- Migrate existing single target_industry_id data to both primary and junction tables
-- For events: set as primary industry AND add to relevant industries
UPDATE public.events 
SET primary_industry_id = target_industry_id 
WHERE target_industry_id IS NOT NULL;

INSERT INTO public.event_relevant_industries (event_id, industry_id)
SELECT id, target_industry_id 
FROM public.events 
WHERE target_industry_id IS NOT NULL;

-- For funding opportunities: set as primary industry AND add to relevant industries
UPDATE public.funding_opportunities 
SET primary_industry_id = target_industry_id 
WHERE target_industry_id IS NOT NULL;

INSERT INTO public.funding_relevant_industries (funding_opportunity_id, industry_id)
SELECT id, target_industry_id 
FROM public.funding_opportunities 
WHERE target_industry_id IS NOT NULL;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_event_relevant_industries_event_id ON public.event_relevant_industries(event_id);
CREATE INDEX IF NOT EXISTS idx_event_relevant_industries_industry_id ON public.event_relevant_industries(industry_id);
CREATE INDEX IF NOT EXISTS idx_funding_relevant_industries_funding_id ON public.funding_relevant_industries(funding_opportunity_id);
CREATE INDEX IF NOT EXISTS idx_funding_relevant_industries_industry_id ON public.funding_relevant_industries(industry_id);

-- Add updated_at triggers
CREATE OR REPLACE FUNCTION update_event_relevant_industries_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION update_funding_relevant_industries_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_event_relevant_industries_updated_at
    BEFORE UPDATE ON public.event_relevant_industries
    FOR EACH ROW
    EXECUTE FUNCTION update_event_relevant_industries_updated_at();

CREATE TRIGGER trigger_update_funding_relevant_industries_updated_at
    BEFORE UPDATE ON public.funding_relevant_industries
    FOR EACH ROW
    EXECUTE FUNCTION update_funding_relevant_industries_updated_at();

-- Grant permissions
GRANT ALL ON TABLE public.event_relevant_industries TO anon;
GRANT ALL ON TABLE public.event_relevant_industries TO authenticated;
GRANT ALL ON TABLE public.event_relevant_industries TO service_role;

GRANT ALL ON TABLE public.funding_relevant_industries TO anon;
GRANT ALL ON TABLE public.funding_relevant_industries TO authenticated;
GRANT ALL ON TABLE public.funding_relevant_industries TO service_role;

-- Add comments for documentation
COMMENT ON COLUMN public.events.primary_industry_id IS 'Primary industry for the event (single select, similar to businesses.main_industry_id)';
COMMENT ON COLUMN public.funding_opportunities.primary_industry_id IS 'Primary industry for the funding opportunity (single select, similar to businesses.main_industry_id)';
COMMENT ON TABLE public.event_relevant_industries IS 'Relevant industries for events (multi-select - industries the event is relevant to, similar to business_target_industries)';
COMMENT ON TABLE public.funding_relevant_industries IS 'Relevant industries for funding opportunities (multi-select - industries the funding is relevant to, similar to business_target_industries)';

-- Note: We keep the target_industry_id columns for now to avoid breaking existing code
-- They can be removed in a future migration once all code is updated
COMMENT ON COLUMN public.events.target_industry_id IS 'DEPRECATED: Use primary_industry_id and event_relevant_industries table instead. Target industry for the event (optional)';
COMMENT ON COLUMN public.funding_opportunities.target_industry_id IS 'DEPRECATED: Use primary_industry_id and funding_relevant_industries table instead. Target industry for the funding opportunity (optional)';
