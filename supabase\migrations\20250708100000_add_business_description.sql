-- Add description field to businesses table
-- This allows businesses to add a description to their listing

-- Add the description column
ALTER TABLE public.businesses 
ADD COLUMN description text;

-- Add comment for documentation
COMMENT ON COLUMN public.businesses.description IS 'Business description displayed on the business detail page';

-- Update the business_directory view to include description
DROP VIEW IF EXISTS "public"."business_directory";

CREATE OR REPLACE VIEW "public"."business_directory" AS
 SELECT "id",
    "business_name",
    "contact_email",
    "contact_phone",
    "website",
    "linkedin",
    "twitter",
    "city",
    "postcode",
    "description",
    "is_verified",
    "created_at",
    "updated_at"
   FROM "public"."businesses";

ALTER VIEW "public"."business_directory" OWNER TO "postgres";

-- Grant permissions
GRANT ALL ON TABLE public.businesses TO anon;
GRANT ALL ON TABLE public.businesses TO authenticated;
GRANT ALL ON TABLE public.businesses TO service_role;
