export interface Database {
  public: {
    Tables: {
      businesses: {
        Row: {
          id: string;
          owner_id: string;
          business_name: string;
          contact_email: string;
          contact_phone?: string | null;
          website?: string | null;
          linkedin?: string | null;
          twitter?: string | null;
          address_line_1?: string | null;
          address_line_2?: string | null;
          city?: string | null;
          postcode?: string | null;
          description?: string | null;
          is_verified: boolean;
          created_at?: string | null;
          updated_at?: string | null;
          // Additional fields that may exist from other migrations
          logo_url?: string | null;
          headquarters_location_id?: string | null;
          main_industry_id?: string | null;
        };
        Insert: {
          id?: string;
          owner_id: string;
          business_name: string;
          contact_email: string;
          contact_phone?: string | null;
          website?: string | null;
          linkedin?: string | null;
          twitter?: string | null;
          address_line_1?: string | null;
          address_line_2?: string | null;
          city?: string | null;
          postcode?: string | null;
          is_verified?: boolean;
          created_at?: string | null;
          updated_at?: string | null;
          logo_url?: string | null;
          headquarters_location_id?: string | null;
          main_industry_id?: string | null;
        };
        Update: {
          id?: string;
          owner_id?: string;
          business_name?: string;
          contact_email?: string;
          contact_phone?: string | null;
          website?: string | null;
          linkedin?: string | null;
          twitter?: string | null;
          address_line_1?: string | null;
          address_line_2?: string | null;
          city?: string | null;
          postcode?: string | null;
          is_verified?: boolean;
          created_at?: string | null;
          updated_at?: string | null;
          logo_url?: string | null;
          headquarters_location_id?: string | null;
          main_industry_id?: string | null;
        };
      };
    };
    Views: {
      business_directory: {
        Row: {
          id: string;
          business_name: string;
          contact_email: string;
          contact_phone?: string | null;
          website?: string | null;
          linkedin?: string | null;
          twitter?: string | null;
          city?: string | null;
          postcode?: string | null;
          is_verified: boolean;
          created_at?: string | null;
          updated_at?: string | null;
        };
      };
    };
  };
}
