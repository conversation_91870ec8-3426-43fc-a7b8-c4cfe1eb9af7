import { useState, useEffect, useMemo } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Plus, Search, Filter, Calendar, Grid, List, Loader2 } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { EventService } from '../services/eventService'
import AddEventForm from '../components/AddEventForm'
import EventCard from '../components/EventCard'
import NetZeroCategoryCards from '@/features/businessDirectory/components/NetZeroCategoryCards'
import IndustryCategoryCards from '@/features/businessDirectory/components/IndustryCategoryCards'
import type {
  EventWithInterests,
  EventFilters,
  EventSortOptions,
  EventLocationType
} from '../types'
import { EVENT_LOCATION_TYPES } from '../types'

const EventsDirectoryPage = () => {
  const [events, setEvents] = useState<EventWithInterests[]>([])
  const [loading, setLoading] = useState(true)
  const [showAddForm, setShowAddForm] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedLocationTypes, setSelectedLocationTypes] = useState<EventLocationType[]>([])
  const [selectedDateRange, setSelectedDateRange] = useState<'all' | 'upcoming' | 'this_month' | 'next_month'>('upcoming')
  const [sortBy, setSortBy] = useState<EventSortOptions>({ field: 'start_date', direction: 'asc' })
  const [viewMode, setViewMode] = useState<'grid' | 'timeline'>('grid')
  const [selectedNetZeroCategoryId, setSelectedNetZeroCategoryId] = useState<string>('')
  const [selectedIndustryCategoryId, setSelectedIndustryCategoryId] = useState<string>('')
  const { toast } = useToast()

  // Load events
  const loadEvents = async () => {
    try {
      setLoading(true)
      
      // Build filters
      const filters: EventFilters = {}
      
      if (searchQuery.trim()) {
        filters.search = searchQuery.trim()
      }
      
      if (selectedLocationTypes.length > 0) {
        filters.location_type = selectedLocationTypes
      }
      
      // Date range filters
      const today = new Date()
      const currentMonth = new Date(today.getFullYear(), today.getMonth(), 1)
      const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, 1)
      const nextMonthEnd = new Date(today.getFullYear(), today.getMonth() + 2, 0)
      
      switch (selectedDateRange) {
        case 'upcoming':
          filters.start_date_from = today.toISOString().split('T')[0]
          break
        case 'this_month':
          filters.start_date_from = currentMonth.toISOString().split('T')[0]
          filters.start_date_to = new Date(today.getFullYear(), today.getMonth() + 1, 0).toISOString().split('T')[0]
          break
        case 'next_month':
          filters.start_date_from = nextMonth.toISOString().split('T')[0]
          filters.start_date_to = nextMonthEnd.toISOString().split('T')[0]
          break
      }

      const response = await EventService.getEvents(filters, sortBy, 1, 100)
      setEvents(response.data)
    } catch (error) {
      console.error('Error loading events:', error)
      toast({
        title: "Error",
        description: "Failed to load events",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  // Load events on component mount and when filters change
  useEffect(() => {
    loadEvents()
  }, [searchQuery, selectedLocationTypes, selectedDateRange, sortBy])

  // Filter events by category selection
  const filteredEvents = useMemo(() => {
    if (!selectedNetZeroCategoryId && !selectedIndustryCategoryId) {
      return events
    }

    return events.filter(event => {
      // Net Zero category filtering
      if (selectedNetZeroCategoryId) {
        if (selectedNetZeroCategoryId === 'not-classified') {
          // Show events with no Net Zero categories
          const hasNoCategories = !(event as any).event_netzero_categories || (event as any).event_netzero_categories.length === 0
          return hasNoCategories
        } else {
          // Show events with matching category
          const hasMatchingCategory = (event as any).event_netzero_categories?.some((cat: any) =>
            cat.subcategory?.category?.id === selectedNetZeroCategoryId
          )
          return hasMatchingCategory
        }
      }

      // Industry category filtering (if implemented)
      if (selectedIndustryCategoryId) {
        // TODO: Add industry filtering logic when industry categories are added to events
        return true
      }

      return true
    })
  }, [events, selectedNetZeroCategoryId, selectedIndustryCategoryId])

  // Group events by month for timeline view
  const eventsByMonth = useMemo(() => {
    const grouped: { [key: string]: EventWithInterests[] } = {}

    filteredEvents.forEach(event => {
      const date = new Date(event.start_date)
      const monthKey = date.toLocaleDateString('en-GB', { year: 'numeric', month: 'long' })

      if (!grouped[monthKey]) {
        grouped[monthKey] = []
      }
      grouped[monthKey].push(event)
    })

    return grouped
  }, [filteredEvents])

  const handleAddSuccess = () => {
    setShowAddForm(false)
    loadEvents()
    toast({
      title: "Success",
      description: "Event added successfully!"
    })
  }

  const toggleLocationType = (locationType: EventLocationType) => {
    setSelectedLocationTypes(prev => 
      prev.includes(locationType)
        ? prev.filter(type => type !== locationType)
        : [...prev, locationType]
    )
  }

  const handleNetZeroCategorySelect = (categoryId: string, categoryName: string) => {
    setSelectedNetZeroCategoryId(categoryId === selectedNetZeroCategoryId ? '' : categoryId)
    // Clear industry filter when selecting net zero category
    if (categoryId !== selectedNetZeroCategoryId) {
      setSelectedIndustryCategoryId('')
    }
  }

  const handleIndustryCategorySelect = (categoryId: string, categoryName: string) => {
    setSelectedIndustryCategoryId(categoryId === selectedIndustryCategoryId ? '' : categoryId)
    // Clear net zero filter when selecting industry category
    if (categoryId !== selectedIndustryCategoryId) {
      setSelectedNetZeroCategoryId('')
    }
  }

  const clearFilters = () => {
    setSearchQuery('')
    setSelectedLocationTypes([])
    setSelectedDateRange('upcoming')
    setSortBy({ field: 'start_date', direction: 'asc' })
    setSelectedNetZeroCategoryId('')
    setSelectedIndustryCategoryId('')
  }

  const hasActiveFilters = searchQuery || selectedLocationTypes.length > 0 || selectedDateRange !== 'upcoming' || selectedNetZeroCategoryId || selectedIndustryCategoryId

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-3xl font-bold mb-2">Events Directory</h1>
            <p className="text-muted-foreground">
              Discover and join net zero events, workshops, and networking opportunities.
            </p>
          </div>

          <Dialog open={showAddForm} onOpenChange={setShowAddForm}>
            <DialogTrigger asChild>
              <Button className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                Add Event
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
              <AddEventForm
                onSuccess={handleAddSuccess}
                onCancel={() => setShowAddForm(false)}
              />
            </DialogContent>
          </Dialog>
        </div>

      {/* Category Cards */}
      <Tabs defaultValue="netzero" className="mb-8">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="netzero">Browse by Net Zero Category</TabsTrigger>
          <TabsTrigger value="industry">Browse by Industry</TabsTrigger>
        </TabsList>
        <TabsContent value="netzero">
          <NetZeroCategoryCards
            contentType="events"
            onCategorySelect={handleNetZeroCategorySelect}
            selectedCategoryId={selectedNetZeroCategoryId}
          />
        </TabsContent>
        <TabsContent value="industry">
          <IndustryCategoryCards
            contentType="events"
            onCategorySelect={handleIndustryCategorySelect}
            selectedCategoryId={selectedIndustryCategoryId}
          />
        </TabsContent>
      </Tabs>

        {/* Search and Filters */}
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-4">
              {/* Search */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search events..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* Filters Row */}
              <div className="flex flex-wrap gap-4 items-center">
                {/* Date Range Filter */}
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">Date:</span>
                  <Select
                    value={selectedDateRange}
                    onValueChange={(value: any) => setSelectedDateRange(value)}
                  >
                    <SelectTrigger className="w-40">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Events</SelectItem>
                      <SelectItem value="upcoming">Upcoming</SelectItem>
                      <SelectItem value="this_month">This Month</SelectItem>
                      <SelectItem value="next_month">Next Month</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Location Type Filters */}
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">Type:</span>
                  <div className="flex gap-2">
                    {EVENT_LOCATION_TYPES.map((type) => (
                      <Button
                        key={type.value}
                        variant={selectedLocationTypes.includes(type.value) ? "default" : "outline"}
                        size="sm"
                        onClick={() => toggleLocationType(type.value)}
                        className="flex items-center gap-1"
                      >
                        <span>{type.icon}</span>
                        <span>{type.label}</span>
                      </Button>
                    ))}
                  </div>
                </div>

                {/* Sort */}
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">Sort:</span>
                  <Select
                    value={`${sortBy.field}-${sortBy.direction}`}
                    onValueChange={(value) => {
                      const [field, direction] = value.split('-') as [any, 'asc' | 'desc']
                      setSortBy({ field, direction })
                    }}
                  >
                    <SelectTrigger className="w-40">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="start_date-asc">Date (Earliest)</SelectItem>
                      <SelectItem value="start_date-desc">Date (Latest)</SelectItem>
                      <SelectItem value="name-asc">Name (A-Z)</SelectItem>
                      <SelectItem value="name-desc">Name (Z-A)</SelectItem>
                      <SelectItem value="interest_count-desc">Most Popular</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* View Mode Toggle */}
                <div className="flex items-center gap-2 ml-auto">
                  <span className="text-sm font-medium">View:</span>
                  <div className="flex border rounded-md">
                    <Button
                      variant={viewMode === 'grid' ? 'default' : 'ghost'}
                      size="sm"
                      onClick={() => setViewMode('grid')}
                      className="rounded-r-none"
                    >
                      <Grid className="h-4 w-4" />
                    </Button>
                    <Button
                      variant={viewMode === 'timeline' ? 'default' : 'ghost'}
                      size="sm"
                      onClick={() => setViewMode('timeline')}
                      className="rounded-l-none"
                    >
                      <List className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {/* Clear Filters */}
                {hasActiveFilters && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={clearFilters}
                    className="flex items-center gap-1"
                  >
                    <Filter className="h-3 w-3" />
                    Clear Filters
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Results */}
      {loading ? (
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      ) : filteredEvents.length === 0 ? (
        <Card>
          <CardContent className="py-12 text-center">
            <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No events found</h3>
            <p className="text-muted-foreground mb-4">
              {hasActiveFilters 
                ? "Try adjusting your filters to see more events."
                : "Be the first to add an event to the directory!"
              }
            </p>
            {hasActiveFilters && (
              <Button variant="outline" onClick={clearFilters}>
                Clear Filters
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-6">
          {/* Results Summary */}
          <div className="flex items-center justify-between">
            <p className="text-sm text-muted-foreground">
              Showing {filteredEvents.length} event{filteredEvents.length !== 1 ? 's' : ''}
            </p>
          </div>

          {/* Events Display */}
          {viewMode === 'grid' ? (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {filteredEvents.map((event) => (
                <EventCard key={event.id} event={event} />
              ))}
            </div>
          ) : (
            <div className="space-y-8">
              {Object.entries(eventsByMonth).map(([month, monthEvents]) => (
                <div key={month}>
                  <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
                    <Calendar className="h-5 w-5" />
                    {month}
                    <Badge variant="secondary" className="ml-2">
                      {monthEvents.length} event{monthEvents.length !== 1 ? 's' : ''}
                    </Badge>
                  </h2>
                  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    {monthEvents.map((event) => (
                      <EventCard key={event.id} event={event} />
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default EventsDirectoryPage
