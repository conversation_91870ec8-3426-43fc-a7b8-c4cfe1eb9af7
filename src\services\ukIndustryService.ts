// UK Industry Service for managing industries and user/business associations
import { supabase } from '@/integrations/supabase/client';
import type { 
  UKIndustry, 
  UKIndustryWithChildren, 
  UKIndustryWithParent,
  IndustryHierarchy,
  UserIndustryResponse,
  BusinessIndustriesResponse,
  IndustrySearchResult,
  BusinessTargetIndustry,
  NewBusinessTargetIndustry
} from '@/types/uk-industries.types';

export class UKIndustryService {
  /**
   * Fetch all industries with their children (hierarchical structure)
   */
  static async getAllIndustriesWithChildren(): Promise<UKIndustryWithChildren[]> {
    const { data: industries, error } = await supabase
      .from('uk_industries')
      .select('*')
      .order('sort_order');

    if (error) {
      throw new Error(`Failed to fetch industries: ${error.message}`);
    }

    // Group children under their parents
    const parentIndustries = industries.filter(industry => !industry.parent_id);
    const childIndustries = industries.filter(industry => industry.parent_id);

    const industriesWithChildren: UKIndustryWithChildren[] = parentIndustries.map(parent => ({
      ...parent,
      children: childIndustries
        .filter(child => child.parent_id === parent.id)
        .sort((a, b) => a.sort_order - b.sort_order)
    }));

    return industriesWithChildren.sort((a, b) => a.sort_order - b.sort_order);
  }

  /**
   * Get industries as a hierarchical structure for UI components
   */
  static async getIndustryHierarchy(): Promise<IndustryHierarchy> {
    const industriesWithChildren = await this.getAllIndustriesWithChildren();
    
    const hierarchy: IndustryHierarchy = {};
    
    industriesWithChildren.forEach(parent => {
      hierarchy[parent.id] = {
        id: parent.id,
        name: parent.name,
        description: parent.description,
        sort_order: parent.sort_order,
        children: {}
      };
      
      parent.children.forEach(child => {
        hierarchy[parent.id].children[child.id] = {
          id: child.id,
          name: child.name,
          description: child.description,
          sort_order: child.sort_order
        };
      });
    });
    
    return hierarchy;
  }

  /**
   * Get all child industries (for selection components)
   */
  static async getChildIndustries(): Promise<UKIndustryWithParent[]> {
    const { data, error } = await supabase
      .from('uk_industries')
      .select(`
        *,
        parent:uk_industries!uk_industries_parent_id_fkey (*)
      `)
      .not('parent_id', 'is', null)
      .order('sort_order');

    if (error) {
      throw new Error(`Failed to fetch child industries: ${error.message}`);
    }

    return data.map(item => ({
      ...item,
      parent: item.parent as UKIndustry
    }));
  }

  /**
   * Get user's industry (from profiles.main_industry_id)
   */
  static async getUserIndustry(userId: string): Promise<UserIndustryResponse> {
    console.log('getUserIndustry called with userId:', userId);
    
    // First, get the user's main_industry_id
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('main_industry_id')
      .eq('id', userId)
      .single();

    console.log('Profile data:', profileData);
    console.log('Profile error:', profileError);

    if (profileError) {
      throw new Error(`Failed to fetch user profile: ${profileError.message}`);
    }

    if (!profileData.main_industry_id) {
      console.log('No main_industry_id found for user');
      return { industry: null };
    }

    // Then get the industry details
    const { data: industryData, error: industryError } = await supabase
      .from('uk_industries')
      .select('*')
      .eq('id', profileData.main_industry_id)
      .single();

    console.log('Industry data:', industryData);
    console.log('Industry error:', industryError);

    if (industryError) {
      throw new Error(`Failed to fetch industry: ${industryError.message}`);
    }

    let industry = null;
    if (industryData) {
      // Get parent if it exists
      let parent = null;
      if (industryData.parent_id) {
        const { data: parentData, error: parentError } = await supabase
          .from('uk_industries')
          .select('*')
          .eq('id', industryData.parent_id)
          .single();
        
        console.log('Parent data:', parentData);
        console.log('Parent error:', parentError);
        
        if (!parentError && parentData) {
          parent = parentData;
        }
      }
      
      industry = {
        ...industryData,
        parent
      };
    }

    console.log('Final industry result:', industry);
    return { industry };
  }

  /**
   * Update user's industry (single select, stored in profiles.main_industry_id)
   */
  static async updateUserIndustry(userId: string, industryId: string | null): Promise<void> {
    const { error } = await supabase
      .from('profiles')
      .update({ main_industry_id: industryId })
      .eq('id', userId);

    if (error) {
      throw new Error(`Failed to update user industry: ${error.message}`);
    }
  }

  /**
   * Get business's industries (main industry + target industries)
   */
  static async getBusinessIndustries(businessId: string): Promise<BusinessIndustriesResponse> {
    // Get business with main industry ID
    const { data: businessData, error: businessError } = await supabase
      .from('businesses')
      .select('main_industry_id')
      .eq('id', businessId)
      .single();

    if (businessError) {
      throw new Error(`Failed to fetch business: ${businessError.message}`);
    }

    let main_industry = null;
    if (businessData.main_industry_id) {
      // Fetch main industry details separately
      const { data: mainIndustryData, error: mainIndustryError } = await supabase
        .from('uk_industries')
        .select('*')
        .eq('id', businessData.main_industry_id)
        .single();

      if (!mainIndustryError && mainIndustryData) {
        // Get parent if it exists
        let parent = null;
        if (mainIndustryData.parent_id) {
          const { data: parentData, error: parentError } = await supabase
            .from('uk_industries')
            .select('*')
            .eq('id', mainIndustryData.parent_id)
            .single();
          
          if (!parentError && parentData) {
            parent = parentData;
          }
        }
        
        main_industry = {
          ...mainIndustryData,
          parent
        };
      }
    }

    // Get target industries - simplified approach
    const { data: targetIndustriesData, error: targetError } = await supabase
      .from('business_target_industries')
      .select('industry_id')
      .eq('business_id', businessId);

    if (targetError) {
      throw new Error(`Failed to fetch target industries: ${targetError.message}`);
    }

    let target_industries: any[] = [];
    if (targetIndustriesData && targetIndustriesData.length > 0) {
      // Get the industry IDs
      const industryIds = targetIndustriesData.map(item => item.industry_id);
      
      // Fetch the industry details separately
      const { data: industriesData, error: industriesError } = await supabase
        .from('uk_industries')
        .select('*')
        .in('id', industryIds);

      if (!industriesError && industriesData) {
        // Get parents for each industry
        target_industries = await Promise.all(
          industriesData.map(async (industry) => {
            let parent = null;
            if (industry.parent_id) {
              const { data: parentData, error: parentError } = await supabase
                .from('uk_industries')
                .select('*')
                .eq('id', industry.parent_id)
                .single();
              
              if (!parentError && parentData) {
                parent = parentData;
              }
            }
            
            return {
              ...industry,
              parent
            };
          })
        );
      }
    }

    return { main_industry, target_industries };
  }

  /**
   * Update business's main industry (single select)
   */
  static async updateBusinessMainIndustry(businessId: string, industryId: string | null): Promise<void> {
    const { error } = await supabase
      .from('businesses')
      .update({ main_industry_id: industryId })
      .eq('id', businessId);

    if (error) {
      throw new Error(`Failed to update main industry: ${error.message}`);
    }
  }

  /**
   * Update business's target industries (multi-select)
   */
  static async updateBusinessTargetIndustries(businessId: string, industryIds: string[]): Promise<void> {
    // First, delete existing target industries
    const { error: deleteError } = await supabase
      .from('business_target_industries')
      .delete()
      .eq('business_id', businessId);

    if (deleteError) {
      throw new Error(`Failed to delete existing target industries: ${deleteError.message}`);
    }

    // Then, insert new target industries if provided
    if (industryIds.length > 0) {
      const newTargetIndustries: NewBusinessTargetIndustry[] = industryIds.map(industryId => ({
        business_id: businessId,
        industry_id: industryId
      }));

      const { error: insertError } = await supabase
        .from('business_target_industries')
        .insert(newTargetIndustries);

      if (insertError) {
        throw new Error(`Failed to insert new target industries: ${insertError.message}`);
      }
    }
  }

  /**
   * Get industry details by ID
   */
  static async getIndustryById(industryId: string): Promise<UKIndustryWithParent | null> {
    const { data, error } = await supabase
      .from('uk_industries')
      .select(`
        *,
        parent:uk_industries!uk_industries_parent_id_fkey (*)
      `)
      .eq('id', industryId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Not found
      }
      throw new Error(`Failed to fetch industry: ${error.message}`);
    }

    return {
      ...data,
      parent: Array.isArray(data.parent) ? data.parent[0] : data.parent
    };
  }

  /**
   * Search industries by name or description
   */
  static async searchIndustries(query: string): Promise<IndustrySearchResult[]> {
    const { data, error } = await supabase
      .from('uk_industries')
      .select(`
        *,
        parent:uk_industries!uk_industries_parent_id_fkey (*)
      `)
      .or(`name.ilike.%${query}%,description.ilike.%${query}%`)
      .order('name');

    if (error) {
      throw new Error(`Failed to search industries: ${error.message}`);
    }

    return data.map(item => ({
      industry: {
        ...item,
        parent: Array.isArray(item.parent) ? item.parent[0] : item.parent
      },
      matchType: item.name.toLowerCase().includes(query.toLowerCase()) ? 'name' : 'description'
    }));
  }

  /**
   * Get parent industries only
   */
  static async getParentIndustries(): Promise<UKIndustry[]> {
    const { data, error } = await supabase
      .from('uk_industries')
      .select('*')
      .is('parent_id', null)
      .order('sort_order');

    if (error) {
      throw new Error(`Failed to fetch parent industries: ${error.message}`);
    }

    return data;
  }

  /**
   * Get business counts for each main industry category
   */
  static async getIndustryBusinessCounts(): Promise<any[]> {
    // Get parent industries with their business counts
    const { data, error } = await supabase
      .from('uk_industries')
      .select(`
        id,
        name,
        description,
        sort_order,
        children:uk_industries!uk_industries_parent_id_fkey (
          id,
          name,
          businesses!businesses_main_industry_id_fkey (id),
          business_target_industries (business_id)
        ),
        businesses!businesses_main_industry_id_fkey (id)
      `)
      .is('parent_id', null)
      .order('sort_order');

    if (error) {
      throw new Error(`Failed to fetch industry business counts: ${error.message}`);
    }

    // Process the data to count unique businesses per industry
    const industryBusinessCounts = data.map(industry => {
      const businessIds = new Set<string>();

      // Add businesses that have this as main industry
      industry.businesses.forEach(business => {
        businessIds.add(business.id);
      });

      // Add businesses from child industries
      industry.children.forEach(child => {
        child.businesses.forEach(business => {
          businessIds.add(business.id);
        });
        child.business_target_industries.forEach(targetIndustry => {
          businessIds.add(targetIndustry.business_id);
        });
      });

      return {
        categoryId: industry.id,
        categoryName: industry.name,
        description: industry.description,
        businessCount: businessIds.size
      };
    });

    return industryBusinessCounts;
  }

  /**
   * Get event counts for each main industry category
   */
  static async getIndustryEventCounts(): Promise<any[]> {
    // Get parent industries with their event counts
    const { data, error } = await supabase
      .from('uk_industries')
      .select(`
        id,
        name,
        description,
        sort_order,
        children:uk_industries!uk_industries_parent_id_fkey (
          id,
          events!events_target_industry_id_fkey (id)
        ),
        events!events_target_industry_id_fkey (id)
      `)
      .is('parent_id', null)
      .order('sort_order');

    if (error) {
      throw new Error(`Failed to fetch industry event counts: ${error.message}`);
    }

    // Process the data to count unique events per industry
    const industryEventCounts = data.map(industry => {
      const eventIds = new Set<string>();

      // Add events that target this industry
      industry.events.forEach(event => {
        eventIds.add(event.id);
      });

      // Add events from child industries
      industry.children.forEach(child => {
        child.events.forEach(event => {
          eventIds.add(event.id);
        });
      });

      return {
        categoryId: industry.id,
        categoryName: industry.name,
        description: industry.description,
        businessCount: eventIds.size // Reusing the same interface, but it's actually event count
      };
    });

    return industryEventCounts;
  }

  /**
   * Get funding opportunity counts for each main industry category
   */
  static async getIndustryFundingCounts(): Promise<any[]> {
    // Get parent industries with their funding opportunity counts
    const { data, error } = await supabase
      .from('uk_industries')
      .select(`
        id,
        name,
        description,
        sort_order,
        children:uk_industries!uk_industries_parent_id_fkey (
          id,
          funding_opportunities!funding_opportunities_target_industry_id_fkey (id)
        ),
        funding_opportunities!funding_opportunities_target_industry_id_fkey (id)
      `)
      .is('parent_id', null)
      .order('sort_order');

    if (error) {
      throw new Error(`Failed to fetch industry funding counts: ${error.message}`);
    }

    // Process the data to count unique funding opportunities per industry
    const industryFundingCounts = data.map(industry => {
      const fundingIds = new Set<string>();

      // Add funding opportunities that target this industry
      industry.funding_opportunities.forEach(funding => {
        fundingIds.add(funding.id);
      });

      // Add funding opportunities from child industries
      industry.children.forEach(child => {
        child.funding_opportunities.forEach(funding => {
          fundingIds.add(funding.id);
        });
      });

      return {
        categoryId: industry.id,
        categoryName: industry.name,
        description: industry.description,
        businessCount: fundingIds.size // Reusing the same interface, but it's actually funding count
      };
    });

    return industryFundingCounts;
  }
}
