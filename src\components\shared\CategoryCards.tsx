import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Loader2, Building2, Calendar, DollarSign, Leaf, Factory, HelpCircle } from 'lucide-react';
import { getNetZeroCategoryTheme, NetZeroCategoryIcon } from '@/utils/netZeroCategoryIcons';
import { getIndustryTheme, IndustryIcon } from '@/utils/industryIcons';

export interface CategoryCount {
  categoryId: string;
  categoryName: string;
  description?: string;
  businessCount: number; // This is actually the count for the specific content type
}

export type CategoryType = 'netzero' | 'industry';
export type ContentType = 'businesses' | 'events' | 'funding';

interface CategoryCardsProps {
  categoryType: CategoryType;
  contentType: ContentType;
  onCategorySelect: (categoryId: string, categoryName: string) => void;
  selectedCategoryId?: string;
  fetchCategoryCounts: () => Promise<CategoryCount[]>;
  title?: string;
  description?: string;
}

const CategoryCards: React.FC<CategoryCardsProps> = ({
  categoryType,
  contentType,
  onCategorySelect,
  selectedCategoryId,
  fetchCategoryCounts,
  title,
  description
}) => {
  const [categories, setCategories] = useState<CategoryCount[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        const counts = await fetchCategoryCounts();
        setCategories(counts);
      } catch (err) {
        console.error('Failed to fetch category counts:', err);
        setError('Failed to load category data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [fetchCategoryCounts]);

  const getContentIcon = () => {
    switch (contentType) {
      case 'businesses':
        return Building2;
      case 'events':
        return Calendar;
      case 'funding':
        return DollarSign;
      default:
        return Building2;
    }
  };

  const getContentLabel = () => {
    switch (contentType) {
      case 'businesses':
        return 'businesses';
      case 'events':
        return 'events';
      case 'funding':
        return 'opportunities';
      default:
        return 'items';
    }
  };

  const getDefaultTitle = () => {
    const categoryLabel = categoryType === 'netzero' ? 'Net Zero Category' : 'Industry';
    const contentLabel = contentType.charAt(0).toUpperCase() + contentType.slice(1);
    return `Browse by ${categoryLabel}`;
  };

  const getDefaultDescription = () => {
    const categoryLabel = categoryType === 'netzero' ? 'sustainability focus area' : 'industry sector';
    const contentLabel = getContentLabel();
    return `Discover ${contentLabel} by their ${categoryLabel}. Click a category to filter the directory.`;
  };

  const getTheme = (categoryName: string) => {
    // Handle "Not Classified" category
    if (categoryName === 'Not Classified') {
      return {
        borderColor: 'border-gray-400',
        textColor: 'text-gray-600',
        hoverBgColor: 'hover:bg-gray-50',
        bgColor: 'bg-gray-50'
      };
    }

    return categoryType === 'netzero'
      ? getNetZeroCategoryTheme(categoryName)
      : getIndustryTheme(categoryName);
  };

  const renderIcon = (categoryName: string, size: 'sm' | 'md' | 'lg' | 'xl' = 'lg', className?: string) => {
    // Handle "Not Classified" category
    if (categoryName === 'Not Classified') {
      const sizeClasses = {
        sm: 'w-4 h-4',
        md: 'w-5 h-5',
        lg: 'w-6 h-6',
        xl: 'w-8 h-8'
      };
      return <HelpCircle className={`${sizeClasses[size]} text-gray-600 ${className || ''}`} />;
    }

    return categoryType === 'netzero'
      ? <NetZeroCategoryIcon categoryName={categoryName} size={size} className={className} />
      : <IndustryIcon industryName={categoryName} size={size} className={className} />;
  };

  const getHeaderIcon = () => {
    return categoryType === 'netzero' ? Leaf : Factory;
  };

  if (loading) {
    return (
      <div className="mb-8">
        <div className="mb-6">
          <h2 className="text-2xl font-bold mb-2 flex items-center gap-2">
            {React.createElement(getHeaderIcon(), { className: "w-6 h-6 text-green-600" })}
            {title || getDefaultTitle()}
          </h2>
          <p className="text-muted-foreground">
            {description || getDefaultDescription()}
          </p>
        </div>
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="mb-8">
        <div className="mb-6">
          <h2 className="text-2xl font-bold mb-2 flex items-center gap-2">
            {React.createElement(getHeaderIcon(), { className: "w-6 h-6 text-green-600" })}
            {title || getDefaultTitle()}
          </h2>
          <p className="text-muted-foreground">
            {description || getDefaultDescription()}
          </p>
        </div>
        <div className="text-center py-8 text-red-600">
          {error}
        </div>
      </div>
    );
  }

  const ContentIcon = getContentIcon();

  return (
    <div className="mb-8">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2 flex items-center gap-2">
          {React.createElement(getHeaderIcon(), { className: "w-6 h-6 text-green-600" })}
          {title || getDefaultTitle()}
        </h2>
        <p className="text-muted-foreground">
          {description || getDefaultDescription()}
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {categories.map((category) => {
          const theme = getTheme(category.categoryName);
          const isSelected = selectedCategoryId === category.categoryId;
          
          return (
            <Card
              key={category.categoryId}
              className={`cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-105 border-2 ${
                isSelected
                  ? `${theme.borderColor} ${theme.hoverBgColor}`
                  : `border-gray-200 hover:${theme.borderColor} ${theme.hoverBgColor}`
              }`}
              onClick={() => onCategorySelect(category.categoryId, category.categoryName)}
            >
              <CardHeader className="pb-3">
                <CardTitle className="text-lg leading-tight flex items-start justify-between">
                  <div className="flex items-center gap-2 flex-1 pr-2">
                    {renderIcon(
                      category.categoryName, 
                      'lg', 
                      isSelected ? theme.textColor : theme.textColor
                    )}
                    <span className={`${isSelected ? `${theme.textColor} font-semibold` : theme.textColor}`}>
                      {category.categoryName}
                    </span>
                  </div>
                  <Badge 
                    variant={isSelected ? "default" : "secondary"}
                    className={`flex items-center gap-1 shrink-0 border ${
                      isSelected 
                        ? `${theme.borderColor} ${theme.textColor}` 
                        : `border-gray-300 ${theme.textColor}`
                    }`}
                  >
                    <ContentIcon className="w-3 h-3" />
                    {category.businessCount}
                  </Badge>
                </CardTitle>
              </CardHeader>
              {category.description && (
                <CardContent className="pt-0">
                  <p className={`text-sm line-clamp-3 ${isSelected ? theme.textColor : 'text-muted-foreground'}`}>
                    {category.description}
                  </p>
                </CardContent>
              )}
            </Card>
          );
        })}
      </div>
    </div>
  );
};

export default CategoryCards;
