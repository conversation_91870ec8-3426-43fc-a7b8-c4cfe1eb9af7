import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useToast } from '@/hooks/use-toast'
import { FundingOpportunityService } from '../services/fundingOpportunityService'
import { NetZeroCategorySelector } from '@/components/netzero/NetZeroCategorySelector'
import { IndustrySelector } from '@/components/industries/IndustrySelector'
import type { FundingOpportunityFormData, FundingType } from '../types'
import { FUNDING_TYPES, CURRENCY_OPTIONS } from '../types'

interface AddFundingOpportunityFormProps {
  onSuccess?: () => void
  onCancel?: () => void
}

const AddFundingOpportunityForm = ({ onSuccess, onCancel }: AddFundingOpportunityFormProps) => {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [selectedNetZeroCategories, setSelectedNetZeroCategories] = useState<string[]>([])
  const [selectedPrimaryIndustry, setSelectedPrimaryIndustry] = useState<string | null>(null)
  const [selectedRelevantIndustries, setSelectedRelevantIndustries] = useState<string[]>([])
  const { toast } = useToast()
  
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
    reset
  } = useForm<FundingOpportunityFormData>({
    defaultValues: {
      funding_type: 'grant',
      currency: 'GBP',
      date_listed: new Date().toISOString().split('T')[0]
    }
  })

  const fundingType = watch('funding_type')

  const onSubmit = async (data: FundingOpportunityFormData) => {
    setIsSubmitting(true)
    
    try {
      // Check if user can add funding opportunities
      const canAdd = await FundingOpportunityService.canUserAddFunding()
      if (!canAdd) {
        toast({
          title: "Permission Denied",
          description: "You don't have permission to add funding opportunities. Check your account settings.",
          variant: "destructive"
        })
        return
      }

      // Prepare data for submission
      const submissionData = {
        ...data,
        date_listed: data.date_listed || new Date().toISOString().split('T')[0],
        deadline_date: data.deadline_date || null,
        amount_min: data.amount_min || null,
        amount_max: data.amount_max || null,
        target_industry_id: selectedPrimaryIndustry, // Backward compatibility
        primary_industry_id: selectedPrimaryIndustry,
        relevant_industry_ids: selectedRelevantIndustries,
        net_zero_categories: selectedNetZeroCategories
      }

      await FundingOpportunityService.createFundingOpportunity(submissionData)
      
      toast({
        title: "Success",
        description: "Funding opportunity has been added successfully!"
      })
      
      reset()
      onSuccess?.()
    } catch (error) {
      console.error('Error creating funding opportunity:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to add funding opportunity",
        variant: "destructive"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Add Funding Opportunity</CardTitle>
        <CardDescription>
          Share a funding opportunity with the community
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="name">Funding Name *</Label>
              <Input
                id="name"
                {...register('name', { required: 'Funding name is required' })}
                placeholder="e.g., Green Innovation Grant 2024"
              />
              {errors.name && (
                <p className="text-sm text-destructive mt-1">{errors.name.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="organization_name">Organisation *</Label>
              <Input
                id="organization_name"
                {...register('organization_name', { required: 'Organisation name is required' })}
                placeholder="e.g., Department for Energy Security and Net Zero"
              />
              {errors.organization_name && (
                <p className="text-sm text-destructive mt-1">{errors.organization_name.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="funding_type">Funding Type *</Label>
              <Select
                value={fundingType}
                onValueChange={(value: FundingType) => setValue('funding_type', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select funding type" />
                </SelectTrigger>
                <SelectContent>
                  {FUNDING_TYPES.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      <div>
                        <div className="font-medium">{type.label}</div>
                        <div className="text-sm text-muted-foreground">{type.description}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Dates */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="date_listed">Date Listed</Label>
              <Input
                id="date_listed"
                type="date"
                {...register('date_listed')}
              />
            </div>

            <div>
              <Label htmlFor="deadline_date">Application Deadline</Label>
              <Input
                id="deadline_date"
                type="date"
                {...register('deadline_date')}
              />
            </div>
          </div>

          {/* Funding Amount */}
          <div className="space-y-4">
            <Label>Funding Amount (Optional)</Label>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="amount_min" className="text-sm">Minimum Amount</Label>
                <Input
                  id="amount_min"
                  type="number"
                  step="0.01"
                  min="0"
                  {...register('amount_min', { valueAsNumber: true })}
                  placeholder="0.00"
                />
              </div>
              
              <div>
                <Label htmlFor="amount_max" className="text-sm">Maximum Amount</Label>
                <Input
                  id="amount_max"
                  type="number"
                  step="0.01"
                  min="0"
                  {...register('amount_max', { valueAsNumber: true })}
                  placeholder="0.00"
                />
              </div>

              <div>
                <Label htmlFor="currency" className="text-sm">Currency</Label>
                <Select
                  value={watch('currency')}
                  onValueChange={(value) => setValue('currency', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {CURRENCY_OPTIONS.map((currency) => (
                      <SelectItem key={currency.value} value={currency.value}>
                        {currency.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* URL */}
          <div>
            <Label htmlFor="url">More Information URL</Label>
            <Input
              id="url"
              type="url"
              {...register('url')}
              placeholder="https://example.com/funding-details"
            />
          </div>

          {/* Description */}
          <div>
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              {...register('description')}
              placeholder="Brief description of the funding opportunity..."
              rows={3}
            />
          </div>

          {/* Eligibility Criteria */}
          <div>
            <Label htmlFor="eligibility_criteria">Eligibility Criteria</Label>
            <Textarea
              id="eligibility_criteria"
              {...register('eligibility_criteria')}
              placeholder="Who is eligible to apply for this funding..."
              rows={3}
            />
          </div>

          {/* Net Zero Categories */}
          <div>
            <Label>Net Zero Categories (Optional)</Label>
            <p className="text-sm text-muted-foreground mb-2">
              Select categories that best describe this funding opportunity to help users find relevant content.
            </p>
            <NetZeroCategorySelector
              selectedSubcategories={selectedNetZeroCategories}
              onSelectionChange={setSelectedNetZeroCategories}
              maxSelections={5}
              title=""
              description=""
            />
          </div>

          {/* Primary Industry */}
          <div>
            <Label>Primary Industry (Optional)</Label>
            <p className="text-sm text-muted-foreground mb-2">
              Select the main industry this funding opportunity is focused on.
            </p>
            <IndustrySelector
              selectedIndustryId={selectedPrimaryIndustry}
              onIndustryChange={setSelectedPrimaryIndustry}
              mode="single"
              title=""
              description=""
              singleSelectLabel="Primary Industry for Funding"
            />
          </div>

          {/* Relevant Industries */}
          <div>
            <Label>Relevant Industries (Optional)</Label>
            <p className="text-sm text-muted-foreground mb-2">
              Select additional industries this funding opportunity is relevant to. You can select multiple industries.
            </p>
            <IndustrySelector
              selectedTargetIndustryIds={selectedRelevantIndustries}
              onTargetIndustriesChange={setSelectedRelevantIndustries}
              mode="multi"
              title=""
              description=""
              multiSelectLabel="Industries This Funding Is Relevant To"
              maxSelections={15}
              showSelectAllButtons={true}
            />
          </div>

          {/* Form Actions */}
          <div className="flex gap-3 pt-4">
            <Button
              type="submit"
              disabled={isSubmitting}
              className="flex-1"
            >
              {isSubmitting ? "Adding..." : "Add Funding Opportunity"}
            </Button>
            
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  )
}

export default AddFundingOpportunityForm
