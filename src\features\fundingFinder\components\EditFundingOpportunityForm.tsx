import React, { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { useToast } from '@/hooks/use-toast'
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { FundingOpportunityService } from '../services/fundingOpportunityService'
import { NetZeroCategorySelector } from '@/components/netzero/NetZeroCategorySelector'
import { IndustrySelector } from '@/components/industries/IndustrySelector'
import { FUNDING_TYPES, CURRENCY_OPTIONS } from '../types'
import type { FundingOpportunity, FundingType } from '../types'

interface FundingOpportunityFormData {
  name: string
  organization_name: string
  funding_type: FundingType
  date_listed: string
  deadline_date: string
  description: string
  eligibility_criteria: string
  url: string
  amount_min: number | null
  amount_max: number | null
  currency: string
}

interface EditFundingOpportunityFormProps {
  opportunity: FundingOpportunity
  onSuccess?: () => void
  onCancel?: () => void
}

const EditFundingOpportunityForm: React.FC<EditFundingOpportunityFormProps> = ({
  opportunity,
  onSuccess,
  onCancel
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [selectedNetZeroCategories, setSelectedNetZeroCategories] = useState<string[]>([])
  const [selectedPrimaryIndustry, setSelectedPrimaryIndustry] = useState<string | null>(null)
  const [selectedRelevantIndustries, setSelectedRelevantIndustries] = useState<string[]>([])
  const { toast } = useToast()

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
    reset
  } = useForm<FundingOpportunityFormData>({
    defaultValues: {
      name: opportunity.name || '',
      organization_name: opportunity.organization_name || '',
      funding_type: opportunity.funding_type || 'grant',
      date_listed: opportunity.date_listed || '',
      deadline_date: opportunity.deadline_date || '',
      description: opportunity.description || '',
      eligibility_criteria: opportunity.eligibility_criteria || '',
      url: opportunity.url || '',
      amount_min: opportunity.amount_min,
      amount_max: opportunity.amount_max,
      currency: opportunity.currency || 'GBP'
    }
  })

  const fundingType = watch('funding_type')

  // Update form values when opportunity changes
  useEffect(() => {
    reset({
      name: opportunity.name || '',
      organization_name: opportunity.organization_name || '',
      funding_type: opportunity.funding_type || 'grant',
      date_listed: opportunity.date_listed || '',
      deadline_date: opportunity.deadline_date || '',
      description: opportunity.description || '',
      eligibility_criteria: opportunity.eligibility_criteria || '',
      url: opportunity.url || '',
      amount_min: opportunity.amount_min,
      amount_max: opportunity.amount_max,
      currency: opportunity.currency || 'GBP'
    })
  }, [opportunity, reset])

  // Load existing categories and industries when opportunity changes
  useEffect(() => {
    const loadExistingData = async () => {
      try {
        // Load existing Net Zero categories
        const fundingWithCategories = await FundingOpportunityService.getFundingOpportunityById(opportunity.id)
        if (fundingWithCategories) {
          // Extract subcategory IDs from the loaded data
          const existingCategoryIds = (fundingWithCategories as any).funding_netzero_categories?.map(
            (cat: any) => cat.subcategory_id
          ) || []
          setSelectedNetZeroCategories(existingCategoryIds)

          // Set primary industry
          if ((fundingWithCategories as any).primary_industry_id) {
            setSelectedPrimaryIndustry((fundingWithCategories as any).primary_industry_id)
          } else if (fundingWithCategories.target_industry_id) {
            // Fallback to old target_industry_id for backward compatibility
            setSelectedPrimaryIndustry(fundingWithCategories.target_industry_id)
          }

          // Load existing relevant industries (when the junction table is available)
          // For now, we'll use the primary industry as the only relevant industry
          const relevantIndustries = []
          if ((fundingWithCategories as any).primary_industry_id) {
            relevantIndustries.push((fundingWithCategories as any).primary_industry_id)
          } else if (fundingWithCategories.target_industry_id) {
            relevantIndustries.push(fundingWithCategories.target_industry_id)
          }
          setSelectedRelevantIndustries(relevantIndustries)
        }
      } catch (error) {
        console.error('Error loading existing funding data:', error)
      }
    }

    if (opportunity.id) {
      loadExistingData()
    }
  }, [opportunity.id])

  const onSubmit = async (data: FundingOpportunityFormData) => {
    setIsSubmitting(true)
    
    try {
      // Prepare data for submission
      const updateData = {
        ...data,
        date_listed: data.date_listed || null,
        deadline_date: data.deadline_date || null,
        amount_min: data.amount_min || null,
        amount_max: data.amount_max || null,
        description: data.description || null,
        eligibility_criteria: data.eligibility_criteria || null,
        url: data.url || null,
        target_industry_id: selectedPrimaryIndustry, // Backward compatibility
        primary_industry_id: selectedPrimaryIndustry,
        relevant_industry_ids: selectedRelevantIndustries,
        net_zero_categories: selectedNetZeroCategories
      }

      await FundingOpportunityService.updateFundingOpportunity(opportunity.id, updateData)
      
      toast({
        title: "Success",
        description: "Funding opportunity has been updated successfully!"
      })
      
      onSuccess?.()
    } catch (error) {
      console.error('Error updating funding opportunity:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update funding opportunity",
        variant: "destructive"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Edit Funding Opportunity</CardTitle>
        <CardDescription>
          Update the details of this funding opportunity
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="name">Funding Name *</Label>
              <Input
                id="name"
                {...register('name', { required: 'Funding name is required' })}
                placeholder="e.g., Green Innovation Grant 2024"
              />
              {errors.name && (
                <p className="text-sm text-destructive mt-1">{errors.name.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="organization_name">Organisation *</Label>
              <Input
                id="organization_name"
                {...register('organization_name', { required: 'Organisation name is required' })}
                placeholder="e.g., Department for Energy Security and Net Zero"
              />
              {errors.organization_name && (
                <p className="text-sm text-destructive mt-1">{errors.organization_name.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="funding_type">Funding Type *</Label>
              <Select
                value={fundingType}
                onValueChange={(value: FundingType) => setValue('funding_type', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select funding type" />
                </SelectTrigger>
                <SelectContent>
                  {FUNDING_TYPES.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      <div>
                        <div className="font-medium">{type.label}</div>
                        <div className="text-sm text-muted-foreground">{type.description}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Dates */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="date_listed">Date Listed</Label>
              <Input
                id="date_listed"
                type="date"
                {...register('date_listed')}
              />
            </div>

            <div>
              <Label htmlFor="deadline_date">Application Deadline</Label>
              <Input
                id="deadline_date"
                type="date"
                {...register('deadline_date')}
              />
            </div>
          </div>

          {/* Funding Amount */}
          <div className="space-y-4">
            <Label>Funding Amount (Optional)</Label>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="amount_min" className="text-sm">Minimum Amount</Label>
                <Input
                  id="amount_min"
                  type="number"
                  step="0.01"
                  min="0"
                  {...register('amount_min', { valueAsNumber: true })}
                  placeholder="0.00"
                />
              </div>
              
              <div>
                <Label htmlFor="amount_max" className="text-sm">Maximum Amount</Label>
                <Input
                  id="amount_max"
                  type="number"
                  step="0.01"
                  min="0"
                  {...register('amount_max', { valueAsNumber: true })}
                  placeholder="0.00"
                />
              </div>

              <div>
                <Label htmlFor="currency" className="text-sm">Currency</Label>
                <Select
                  value={watch('currency')}
                  onValueChange={(value) => setValue('currency', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {CURRENCY_OPTIONS.map((currency) => (
                      <SelectItem key={currency.value} value={currency.value}>
                        {currency.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* URL */}
          <div>
            <Label htmlFor="url">More Information URL</Label>
            <Input
              id="url"
              type="url"
              {...register('url')}
              placeholder="https://example.com/funding-details"
            />
          </div>

          {/* Description */}
          <div>
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              {...register('description')}
              placeholder="Brief description of the funding opportunity..."
              rows={3}
            />
          </div>

          {/* Eligibility Criteria */}
          <div>
            <Label htmlFor="eligibility_criteria">Eligibility Criteria</Label>
            <Textarea
              id="eligibility_criteria"
              {...register('eligibility_criteria')}
              placeholder="Who is eligible to apply for this funding..."
              rows={3}
            />
          </div>

          {/* Net Zero Categories */}
          <div>
            <Label>Net Zero Categories (Optional)</Label>
            <p className="text-sm text-muted-foreground mb-2">
              Select categories that best describe this funding opportunity to help users find relevant content.
            </p>
            <NetZeroCategorySelector
              selectedSubcategories={selectedNetZeroCategories}
              onSelectionChange={setSelectedNetZeroCategories}
              maxSelections={5}
              title=""
              description=""
            />
          </div>

          {/* Primary Industry */}
          <div>
            <Label>Primary Industry (Optional)</Label>
            <p className="text-sm text-muted-foreground mb-2">
              Select the main industry this funding opportunity is focused on.
            </p>
            <IndustrySelector
              selectedIndustryId={selectedPrimaryIndustry}
              onIndustryChange={setSelectedPrimaryIndustry}
              mode="single"
              title=""
              description=""
              singleSelectLabel="Primary Industry for Funding"
            />
          </div>

          {/* Relevant Industries */}
          <div>
            <Label>Relevant Industries (Optional)</Label>
            <p className="text-sm text-muted-foreground mb-2">
              Select additional industries this funding opportunity is relevant to. You can select multiple industries.
            </p>
            <IndustrySelector
              selectedTargetIndustryIds={selectedRelevantIndustries}
              onTargetIndustriesChange={setSelectedRelevantIndustries}
              mode="multi"
              title=""
              description=""
              multiSelectLabel="Industries This Funding Is Relevant To"
              maxSelections={15}
              showSelectAllButtons={true}
            />
          </div>

          {/* Form Actions */}
          <div className="flex gap-3 pt-4">
            <Button
              type="submit"
              disabled={isSubmitting}
              className="flex-1"
            >
              {isSubmitting ? "Updating..." : "Update Funding Opportunity"}
            </Button>
            
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  )
}

export default EditFundingOpportunityForm
