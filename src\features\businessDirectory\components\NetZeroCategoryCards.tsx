import React from 'react';
import CategoryCards from '@/components/shared/CategoryCards';
import { NetZeroCategoryService } from '@/services/netZeroCategoryService';
import type { CategoryCount, ContentType } from '@/components/shared/CategoryCards';

interface NetZeroCategoryCardsProps {
  contentType?: ContentType;
  onCategorySelect: (categoryId: string, categoryName: string) => void;
  selectedCategoryId?: string;
  title?: string;
  description?: string;
}

const NetZeroCategoryCards: React.FC<NetZeroCategoryCardsProps> = ({
  contentType = 'businesses',
  onCategorySelect,
  selectedCategoryId,
  title,
  description
}) => {
  const fetchCategoryCounts = async (): Promise<CategoryCount[]> => {
    let categoryCounts: CategoryCount[] = [];
    let uncategorizedCount = 0;

    switch (contentType) {
      case 'businesses':
        categoryCounts = await NetZeroCategoryService.getCategoryBusinessCounts();
        uncategorizedCount = await NetZeroCategoryService.getUncategorizedBusinessCount();
        break;
      case 'events':
        categoryCounts = await NetZeroCategoryService.getCategoryEventCounts();
        uncategorizedCount = await NetZeroCategoryService.getUncategorizedEventCount();
        break;
      case 'funding':
        categoryCounts = await NetZeroCategoryService.getCategoryFundingCounts();
        uncategorizedCount = await NetZeroCategoryService.getUncategorizedFundingCount();
        break;
      default:
        categoryCounts = await NetZeroCategoryService.getCategoryBusinessCounts();
        uncategorizedCount = await NetZeroCategoryService.getUncategorizedBusinessCount();
        break;
    }

    // Add "Not Classified" category if there are uncategorized items
    if (uncategorizedCount > 0) {
      categoryCounts.push({
        categoryId: 'not-classified',
        categoryName: 'Not Classified',
        description: `${contentType.charAt(0).toUpperCase() + contentType.slice(1)} that haven't been assigned to any Net Zero category yet`,
        businessCount: uncategorizedCount
      });
    }

    return categoryCounts;
  };

  return (
    <CategoryCards
      categoryType="netzero"
      contentType={contentType}
      onCategorySelect={onCategorySelect}
      selectedCategoryId={selectedCategoryId}
      fetchCategoryCounts={fetchCategoryCounts}
      title={title}
      description={description}
    />
  );
};

export default NetZeroCategoryCards;
