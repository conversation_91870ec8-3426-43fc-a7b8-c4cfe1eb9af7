import { useState, useEffect, useMemo } from 'react'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Search, Building, Loader2 } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { FundingOpportunityService } from '../services/fundingOpportunityService'
import FundingOpportunityCard from '../components/FundingOpportunityCard'
import FundingStatsCounters from '../components/FundingStatsCounters'
import NetZeroCategoryCards from '@/features/businessDirectory/components/NetZeroCategoryCards'
import IndustryCategoryCards from '@/features/businessDirectory/components/IndustryCategoryCards'
import type {
  FundingOpportunityWithInterests,
  FundingOpportunityFilters,
  FundingOpportunitySortOptions,
  FundingType
} from '../types'
import { FUNDING_TYPES } from '../types'

const FundingFinderPage = () => {
  const [allOpportunities, setAllOpportunities] = useState<FundingOpportunityWithInterests[]>([])
  const [loading, setLoading] = useState(true)
  const [searching, setSearching] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('')
  const [selectedType, setSelectedType] = useState<FundingType | 'all'>('all')
  const [sortBy, setSortBy] = useState<FundingOpportunitySortOptions>({
    field: 'date_listed',
    direction: 'desc'
  })
  const [selectedNetZeroCategoryId, setSelectedNetZeroCategoryId] = useState<string>('')
  const [selectedIndustryCategoryId, setSelectedIndustryCategoryId] = useState<string>('')
  const { toast } = useToast()

  // Reduced debounce time for faster response
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery)
    }, 300) // Reduced from 500ms to 300ms

    return () => clearTimeout(timer)
  }, [searchQuery])

  // Client-side filtering for instant results when typing
  const filteredOpportunities = useMemo(() => {
    let filtered = [...allOpportunities]

    // Apply type filter
    if (selectedType !== 'all') {
      filtered = filtered.filter(opp => opp.funding_type === selectedType)
    }

    // Apply Net Zero category filter
    if (selectedNetZeroCategoryId) {
      filtered = filtered.filter(opp => {
        if (selectedNetZeroCategoryId === 'not-classified') {
          // Show funding with no Net Zero categories
          const hasNoCategories = !(opp as any).funding_netzero_categories || (opp as any).funding_netzero_categories.length === 0
          return hasNoCategories
        } else {
          // Show funding with matching category
          const hasMatchingCategory = (opp as any).funding_netzero_categories?.some((cat: any) =>
            cat.subcategory?.category?.id === selectedNetZeroCategoryId
          )
          return hasMatchingCategory
        }
      })
    }

    // Apply industry category filter (if implemented)
    if (selectedIndustryCategoryId) {
      // TODO: Add industry filtering logic when industry categories are added to funding
    }

    // Apply client-side search for instant feedback
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase().trim()
      filtered = filtered.filter(opp =>
        opp.name.toLowerCase().includes(query) ||
        opp.organization_name.toLowerCase().includes(query) ||
        opp.description?.toLowerCase().includes(query) ||
        opp.eligibility_criteria?.toLowerCase().includes(query)
      )
    }

    // Apply sorting
    filtered.sort((a, b) => {
      const { field, direction } = sortBy

      let aValue: any = a[field as keyof typeof a]
      let bValue: any = b[field as keyof typeof b]

      // Handle different data types
      if (field.includes('date')) {
        aValue = new Date(aValue || 0).getTime()
        bValue = new Date(bValue || 0).getTime()
      } else if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase()
        bValue = bValue?.toLowerCase() || ''
      }

      if (direction === 'asc') {
        return aValue > bValue ? 1 : -1
      } else {
        return aValue < bValue ? 1 : -1
      }
    })

    return filtered
  }, [allOpportunities, searchQuery, selectedType, sortBy, selectedNetZeroCategoryId, selectedIndustryCategoryId])

  const loadAllOpportunities = async (forceRefresh = false) => {
    try {
      if (forceRefresh) setLoading(true)
      if (debouncedSearchQuery) setSearching(true)

      const filters: FundingOpportunityFilters = {}

      // Only use server-side search for debounced queries
      if (debouncedSearchQuery.trim()) {
        filters.search_query = debouncedSearchQuery.trim()
      }

      const response = await FundingOpportunityService.getFundingOpportunities(
        filters,
        { field: 'date_listed', direction: 'desc' }, // Always load newest first from server
        1,
        100 // Load more opportunities for better client-side filtering
      )

      // Get user interests for each opportunity
      const opportunitiesWithUserInterests = await Promise.all(
        response.data.map(async (opportunity) => {
          try {
            const userInterest = await FundingOpportunityService.getUserInterest(opportunity.id)
            return {
              ...opportunity,
              user_interest: userInterest
            }
          } catch (error) {
            return opportunity
          }
        })
      )

      setAllOpportunities(opportunitiesWithUserInterests)
    } catch (error) {
      console.error('Error loading funding opportunities:', error)
      toast({
        title: "Error",
        description: "Failed to load funding opportunities",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
      setSearching(false)
    }
  }

  // Initial load
  useEffect(() => {
    loadAllOpportunities(true)
  }, [])

  // Only reload from server when debounced search changes significantly
  useEffect(() => {
    // Only fetch from server if search query is substantial or empty
    if (debouncedSearchQuery.length === 0 || debouncedSearchQuery.length >= 3) {
      loadAllOpportunities()
    }
  }, [debouncedSearchQuery])

  const activeOpportunities = filteredOpportunities.filter(opp => {
    if (!opp.deadline_date) return true
    return new Date(opp.deadline_date) >= new Date()
  })

  const expiredOpportunities = filteredOpportunities.filter(opp => {
    if (!opp.deadline_date) return false
    return new Date(opp.deadline_date) < new Date()
  })

  const handleNetZeroCategorySelect = (categoryId: string, categoryName: string) => {
    setSelectedNetZeroCategoryId(categoryId === selectedNetZeroCategoryId ? '' : categoryId)
    // Clear industry filter when selecting net zero category
    if (categoryId !== selectedNetZeroCategoryId) {
      setSelectedIndustryCategoryId('')
    }
  }

  const handleIndustryCategorySelect = (categoryId: string, categoryName: string) => {
    setSelectedIndustryCategoryId(categoryId === selectedIndustryCategoryId ? '' : categoryId)
    // Clear net zero filter when selecting industry category
    if (categoryId !== selectedIndustryCategoryId) {
      setSelectedNetZeroCategoryId('')
    }
  }

  // Debug effect to log animation trigger conditions
  useEffect(() => {
    console.log('FundingFinder debug:', {
      loading,
      opportunitiesCount: allOpportunities.length,
      startAnimation: !loading && allOpportunities.length > 0
    });
  }, [loading, allOpportunities.length]);

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="mb-4">
          <h1 className="text-3xl font-bold mb-2">Funding Finder</h1>
          <p className="text-muted-foreground">
            Discover grants, loans, tenders, and investment opportunities for sustainable projects.
          </p>
        </div>

        {/* Stats Counters - Only start animation when opportunities are loaded */}
        <FundingStatsCounters
          className="mb-6"
          startAnimation={!loading && allOpportunities.length > 0}
        />
      </div>

      {/* Category Cards */}
      <Tabs defaultValue="netzero" className="mb-8">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="netzero">Browse by Net Zero Category</TabsTrigger>
          <TabsTrigger value="industry">Browse by Industry</TabsTrigger>
        </TabsList>
        <TabsContent value="netzero">
          <NetZeroCategoryCards
            contentType="funding"
            onCategorySelect={handleNetZeroCategorySelect}
            selectedCategoryId={selectedNetZeroCategoryId}
          />
        </TabsContent>
        <TabsContent value="industry">
          <IndustryCategoryCards
            contentType="funding"
            onCategorySelect={handleIndustryCategorySelect}
            selectedCategoryId={selectedIndustryCategoryId}
          />
        </TabsContent>
      </Tabs>

      <div className="mb-8">
        {/* Filters and Search */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col md:flex-row gap-4">
              {/* Search */}
              <div className="flex-1">
                <div className="relative">
                  {searching ? (
                    <Loader2 className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground animate-spin" />
                  ) : (
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  )}
                  <Input
                    placeholder="Search funding opportunities..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 pr-4"
                  />
                  {searchQuery && (
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-xs text-muted-foreground">
                      {filteredOpportunities.length} result{filteredOpportunities.length !== 1 ? 's' : ''}
                    </div>
                  )}
                </div>
              </div>

              {/* Type Filter */}
              <div className="w-full md:w-48">
                <Select
                  value={selectedType}
                  onValueChange={(value: FundingType | 'all') => setSelectedType(value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All Types" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    {FUNDING_TYPES.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Sort */}
              <div className="w-full md:w-48">
                <Select
                  value={`${sortBy.field}-${sortBy.direction}`}
                  onValueChange={(value) => {
                    const [field, direction] = value.split('-') as [any, 'asc' | 'desc']
                    setSortBy({ field, direction })
                  }}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="date_listed-desc">Newest First</SelectItem>
                    <SelectItem value="date_listed-asc">Oldest First</SelectItem>
                    <SelectItem value="deadline_date-asc">Deadline Soon</SelectItem>
                    <SelectItem value="name-asc">Name A-Z</SelectItem>
                    <SelectItem value="organization_name-asc">Organisation A-Z</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="text-center py-12">
          <p className="text-muted-foreground">Loading funding opportunities...</p>
        </div>
      )}

      {/* No Results */}
      {!loading && filteredOpportunities.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <Building className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No funding opportunities found</h3>
            <p className="text-muted-foreground">
              {searchQuery || selectedType !== 'all'
                ? "Try adjusting your search or filters"
                : "Check back later for new funding opportunities!"
              }
            </p>
          </CardContent>
        </Card>
      )}

      {/* Active Opportunities */}
      {!loading && activeOpportunities.length > 0 && (
        <div className="mb-8">
          <div className="flex items-center gap-2 mb-4">
            <h2 className="text-xl font-semibold">Active Opportunities</h2>
            <Badge variant="secondary">{activeOpportunities.length}</Badge>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {activeOpportunities.map((opportunity) => (
              <FundingOpportunityCard
                key={opportunity.id}
                opportunity={opportunity}
              />
            ))}
          </div>
        </div>
      )}

      {/* Expired Opportunities */}
      {!loading && expiredOpportunities.length > 0 && (
        <div>
          <div className="flex items-center gap-2 mb-4">
            <h2 className="text-xl font-semibold text-muted-foreground">Past Opportunities</h2>
            <Badge variant="outline">{expiredOpportunities.length}</Badge>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {expiredOpportunities.map((opportunity) => (
              <FundingOpportunityCard
                key={opportunity.id}
                opportunity={opportunity}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

export default FundingFinderPage
